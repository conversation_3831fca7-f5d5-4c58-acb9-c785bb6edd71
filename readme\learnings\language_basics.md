# NetLinx Programming Language Basics

## Introduction
NetLinx is a programming language developed by AMX (now part of Harman) for controlling audio/visual and automation systems. It's specifically designed for the NI-3100 and other AMX controllers. This guide covers the fundamental concepts based on the hotel suite automation system example.

## 1. Program Structure

### Basic Program Layout
```netlinx
PROGRAM_NAME='Your Program Name'
(* Comments go here *)

DEFINE_DEVICE
// Device definitions

DEFINE_CONSTANT
// Constants

DEFINE_TYPE
// Custom data types

DEFINE_VARIABLE
// Variable declarations

DEFINE_FUNCTION
// Function definitions

DEFINE_START
// Startup code

DEFINE_EVENT
// Event handlers

DEFINE_PROGRAM
// Main program loop
```

## 2. Comments
- Single line: `// This is a comment`
- Multi-line: `(* This is a multi-line comment *)`
- Header comments: Use `(**********************************************************)` for sections

## 3. Device Definitions (DEFINE_DEVICE)

Devices are the heart of NetLinx - they represent physical hardware or virtual devices.

### Physical Devices
```netlinx
DEFINE_DEVICE
dvSTB_Bed = 0:3:0          // IP device on port 3
dvSW = 5001:1:0            // Serial device, port 1 on device 5001
dvTP = 10001:1:0           // Touch panel
dvLCDBed = 5001:2:0        // LCD TV on port 2
```

### Virtual Devices
```netlinx
vdvSTB_Bed = 34001:1:0     // Virtual device for set-top box
virtual = 35100:1:0        // Generic virtual device
```

### Device Number Format: `Device:Port:System`
- **Device**: Physical device number (0 for IP, 5001 for serial devices, etc.)
- **Port**: Port number on the device
- **System**: System number (usually 0 for local system)

## 4. Constants (DEFINE_CONSTANT)

Constants make code readable and maintainable:

```netlinx
DEFINE_CONSTANT
SOURCE_TV = 1
SOURCE_DVD = 2
SOURCE_VOD = 3
SOURCE_MUSIC = 4
SOURCE_AUX = 5
SOURCE_NONE = 6

TRUE = 1
FALSE = 0
TCP = 1
UDP = 2
```

## 5. Variables (DEFINE_VARIABLE)

### Data Types
```netlinx
DEFINE_VARIABLE
CHAR strGuestName[64]           // String variable (64 characters max)
INTEGER nSrcSel_Bed             // Integer variable
FLOAT fCurrentTemp              // Floating point number
VOLATILE CHAR strBuffer[1000]   // Volatile string (changes frequently)
CONSTANT CHAR cAddress[] = '*********'  // Constant string
```

### Variable Naming Conventions
- `str` prefix for strings: `strGuestName`
- `n` prefix for integers: `nSrcSel_Bed`
- `f` prefix for floats: `fCurrentTemp`
- `dv` prefix for devices: `dvTP`
- `vdv` prefix for virtual devices: `vdvSTB_Bed`

## 6. Functions (DEFINE_FUNCTION)

### Function Declaration
```netlinx
DEFINE_FUNCTION INTEGER fnTCP_ClientConnect(INTEGER nLivingOrBed)
{
    SLONG slResult

    SWITCH(nLivingOrBed)
    {
        CASE CONST_LIVING:
        {
            // Living room connection code
            RETURN 1
        }
        CASE CONST_BED:
        {
            // Bedroom connection code
            RETURN 1
        }
    }
}
```

### Function Features
- Return types: `INTEGER`, `CHAR`, `FLOAT`, or no return type
- Parameters in parentheses
- Local variables declared with `STACK_VAR`
- Use `RETURN` to return values

## 7. Control Structures

### IF Statements
```netlinx
IF (nLCDBedPwrStat = 0)
{
    // Turn on LCD
    SEND_STRING dvLCDBed,"'ka 1 1',13"
}
ELSE
{
    // Turn off LCD
    SEND_STRING dvLCDBed,"'ka 1 0',13"
}
```

### SWITCH Statements
```netlinx
SWITCH(nRoomSel)
{
    CASE 1:
    {
        // Living room code
    }
    CASE 2:
    {
        // Bedroom code
    }
    DEFAULT:
    {
        // Default case
    }
}
```

### SELECT Statements (NetLinx specific)
```netlinx
SELECT
{
    ACTIVE(find_string(strMsg,'TV',1)):
    {
        // Handle TV command
    }
    ACTIVE(find_string(strMsg,'DVD',1)):
    {
        // Handle DVD command
    }
}
```

## 8. Device Communication

### Sending Strings
```netlinx
SEND_STRING dvSTB_Living,'$TV#'        // Send command to device
SEND_STRING 0,'Debug message'          // Send to diagnostic output
```

### Sending Commands
```netlinx
SEND_COMMAND dvTP,"'PAGE-WELCOME'"     // Send command to touch panel
SEND_COMMAND dvSW,'SET BAUD 9600, N, 8, 1, 485 DISABLE'  // Configure serial
```

### Channel Operations
```netlinx
ON[dvDND,6]                // Turn on channel 6
OFF[dvDND,6]               // Turn off channel 6
PULSE[dvDVD,1]             // Pulse channel 1
TO[dvVol_Living,1]         // Momentary on channel 1
[dvTP,58] = (nLCDLivPwrStat == 1)  // Set channel based on condition
```

## 9. Events (DEFINE_EVENT)

### Data Events
```netlinx
DATA_EVENT[dvSTB_Living]
{
    ONLINE:
    {
        // Device came online
        nIPStatusLiving = IPCONN_CONNECTED
    }
    STRING:
    {
        // Received string data
        fnParseServerMessage(data.text, CONST_LIVING)
    }
    OFFLINE:
    {
        // Device went offline
        nIPStatusLiving = IPCONN_DISCONNECTED
    }
    ONERROR:
    {
        // Handle errors
        SEND_STRING 0,"'Error: ',ITOA(data.number)"
    }
}
```

### Button Events
```netlinx
BUTTON_EVENT[dvTP,1]  // Button 1 on touch panel
{
    PUSH:
    {
        // Button was pressed
        SEND_STRING dvSTB_Living,'$TV#'
    }
    RELEASE:
    {
        // Button was released
    }
    HOLD[30]:
    {
        // Button held for 3 seconds (30 * 0.1s)
    }
}
```

## 10. Timing Functions

### WAIT Statements
```netlinx
WAIT 20                    // Wait 2 seconds (20 * 0.1s)
WAIT 60 'Check Power'      // Named wait (can be cancelled)
{
    SEND_STRING dvLCDBed,"'ka 1 FF',13"
}

CANCEL_WAIT 'Check Power'  // Cancel named wait
```

### Timeline (for repeating events)
```netlinx
DEFINE_CONSTANT
LONG lTimeArray[] = {1000, 2000, 3000}  // 1s, 2s, 3s intervals

TIMELINE_EVENT[1]
{
    SWITCH(TIMELINE.SEQUENCE)
    {
        CASE 1: // After 1 second
        CASE 2: // After 2 seconds
        CASE 3: // After 3 seconds
    }
}
```

## 11. String Functions

### Common String Operations
```netlinx
CHAR strBuffer[255]
CHAR strMsg[100]

strMsg = REMOVE_STRING(strBuffer,'#',1)    // Remove up to '#'
SET_LENGTH_STRING(strMsg, 10)              // Set string length
INTEGER nPos = FIND_STRING(strMsg,'TV',1)  // Find substring
CHAR strUpper[] = UPPER_STRING(strMsg)     // Convert to uppercase
INTEGER nLen = LENGTH_STRING(strMsg)       // Get string length
```

## 12. Mathematical Functions

```netlinx
INTEGER nResult = ATOI('123')              // String to integer
CHAR strResult[10] = ITOA(123)             // Integer to string
FLOAT fResult = ATOF('123.45')             // String to float
CHAR strFloat[10] = FTOA(123.45)           // Float to string
```

## 13. IP Communication

### Opening IP Connections
```netlinx
SLONG slResult = IP_CLIENT_OPEN(dvSTB_Living.port, '*********', 8221, TCP)
IF(slResult)
{
    // Handle connection error
}
```

### Creating Buffers
```netlinx
DEFINE_START
CREATE_BUFFER dvSTB_Living, strLivroomServerBuffer
```

## 14. Common Patterns

### Error Handling
```netlinx
IF(nIPStatusLiving <> IPCONN_DISCONNECTED)
{
    RETURN 0  // Exit function early
}
```

### State Management
```netlinx
// Use integers to track states
INTEGER nLCDBedPwrStat  // 0=off, 1=on, 2=transitioning
```

### Command Queuing
```netlinx
// Store commands when device offline
IF(nIPStatusLiving == IPCONN_CONNECTED)
    SEND_STRING dvSTB_Living,'$TV#'
ELSE
    strLivingroomCommand = '$TV#'  // Queue for later
```

## 15. Real Examples from the Hotel Suite System

### Device Control Example
```netlinx
// From the example file - TV power control
IF(nLCDBedPwrStat = 0)
{
    cancel_wait 'Check LCDBed Power'
    SEND_STRING dvLCDBed,"'ka 1 1',13"    // Turn on TV
    PULSE[dvLCDBedIR,27]                  // Send IR power command
    WAIT 60
    {
        SEND_STRING dvLCDBed,"'kb 1 60',13"  // Set input after delay
    }
    nLCDBedPwrStat = 1
}
```

### Message Parsing Example
```netlinx
// From fnParseServerMessage function
define_function fnParseServerMessage (CHAR strBuffer[], INTEGER nRoom)
STACK_VAR
  char strMsg[255]
{
  strMsg = remove_string(strBuffer,'#',1)

  select
  {
    active (find_string(strMsg,'TV',1)):
    {
        // Handle TV command
        nSrcSel_Bed = SOURCE_TV
        SEND_STRING dvSW,'2*2$'  // Switch video
    }
    active (find_string(strMsg,'DVD',1)):
    {
        // Handle DVD command
        nSrcSel_Bed = SOURCE_DVD
        SEND_STRING dvSW,'3*2!'  // Switch to DVD input
    }
  }
}
```

### Button Event Example
```netlinx
// From the touch panel button events
BUTTON_EVENT[dvTP,1]          // TV Select button
{
  PUSH:
  {
    SWITCH (nRoomSel)
    {
        CASE 1:  // Living room
        {
            if (nIPStatusLiving == ipconn_connected)
                SEND_STRING dvSTB_Living,'$TV#'
            else
                strLivingroomCommand = '$TV#'
        }
        CASE 2:  // Bedroom
        {
            if (nIPStatusBed == ipconn_connected)
                send_string dvSTB_Bed,'$TV#'
            else
                strBedroomCommand = '$TV#'
        }
    }
  }
}
```

## 16. Best Practices for NI-3100

1. **Use meaningful variable names** with prefixes (`str`, `n`, `dv`, `vdv`)
2. **Comment your code** extensively using `//` and `(* *)`
3. **Handle device offline states** gracefully with command queuing
4. **Use constants** instead of magic numbers for readability
5. **Implement error handling** for IP connections and device communication
6. **Use WAIT statements** for timing-critical operations
7. **Create reusable functions** for common tasks like connection management
8. **Test thoroughly** on actual hardware before deployment
9. **Use SELECT statements** for complex string parsing
10. **Implement proper state management** for device status tracking

## 17. Key NetLinx Concepts for Beginners

### Device-Centric Programming
- Everything revolves around devices (physical or virtual)
- Devices communicate through strings, commands, and channels
- Each device has multiple channels (like GPIO pins)

### Event-Driven Architecture
- Programs respond to events (device online/offline, button presses, data received)
- Main program loop runs continuously in DEFINE_PROGRAM
- Events are handled asynchronously

### String-Based Communication
- Most device communication uses string commands
- Protocols are often custom and device-specific
- String parsing is crucial for interpreting responses

### Timing is Critical
- Use WAIT statements for delays and sequencing
- CANCEL_WAIT to stop pending operations
- Named waits allow better control

## 18. Common Beginner Mistakes

1. **Forgetting to handle device offline states**
2. **Not using proper variable naming conventions**
3. **Hardcoding values instead of using constants**
4. **Not implementing error handling for IP connections**
5. **Mixing up device numbers and port numbers**
6. **Not understanding the timing of WAIT statements**
7. **Forgetting to create buffers for string devices**

## 19. Next Steps for Learning

1. **Study the complete example file** for real-world patterns
2. **Practice with simple device control programs**
3. **Learn about AMX device protocols** and communication standards
4. **Understand touch panel programming** and user interface design
5. **Explore advanced features** like modules, includes, and libraries
6. **Join AMX programming communities** for support and best practices
7. **Practice on actual NI-3100 hardware** when possible

## 20. Resources

- **AMX Programming Guide**: Official documentation from AMX/Harman
- **NetLinx Language Reference**: Complete language specification
- **AMX Forums**: Community support and code examples
- **Device Protocol Documents**: Specific to each manufacturer's equipment

This foundation will help you start programming your NI-3100 controller effectively. The hotel suite example demonstrates real-world complexity and professional coding practices that you should emulate in your own projects.