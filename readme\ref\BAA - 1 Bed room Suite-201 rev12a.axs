PROGRAM_NAME='BAA - 1 Bed room Suite-201 rev 12a'
(***********************************************************)
(*  FILE_LAST_MODIFIED_ON: 04/27/2009  AT: 15:15:31        *)
(***********************************************************)
(***********************************************************)
(* System Type : NetLinx                                   *)
(***********************************************************)
(* REV HISTORY:                                            *)
(***********************************************************)
(*																												 *)
(*		NI-3100				*********1														 *)
(*		AXIS					*********2														 *)
(*		WAP200G				*********3														 *)
(*		MVP-8400			*********6  													 *)
(*																												 *)
(***********************************************************)
(*          DEVICE NUMBER DEFINITIONS GO BELOW             *)
(***********************************************************)
DEFINE_DEVICE

// IP Servers
dvSTB_Bed			= 0:3:0				//*********
dvSTB_Living	= 0:4:0 			//*********

vdvSTB_Bed		= 34001:1:0
vdvSTB_Living	= 34002:1:0

dvServer			= 0:6:0


// RMS Server - all code is in RMSMain.axi file
//#IF_NOT_DEFINED dvRMSSocket
//dvRMSSocket	= 0:5:0;
//#END_IF

dvSW					= 5001:1:0		//8 x 4 Matrix Switcher
dvLCDBed 			= 5001:2:0		//LCD TV 32 LG
dvLCDLiv			= 5001:3:0  	//LCD TV 42 LG

vdvSW					= 35001:1:0
vdvLCDBed			= 35002:1:0
vdvLCDLiv			= 35003:1:0

dvCurtainBR		= 5001:8:0		//Relay for Curtain
dvDoor				= 5001:8:0		//Relay for Door
dvDND					= 5001:8:0		//Relay for Do not disturb
dvDoorBell		= 5001:8:0		//Relay for Doorbell
dvLift				= 5001:8:0		//Relay for Lift
dvDVD					= 5001:9:0 		//DVD player
dvLCDBedIR		= 5001:10:0		//LCD TV 32 LG
dvLCDLivIR		= 5001:9:0  	//LCD TV 42 LG

dvAlarm_IO		= 5001:17:0		//I/O port for Fire Alarm
dvBell_IO			= 5001:17:0		//I/O port for Door bell
dvLift_IO			= 5001:17:0		//Relay for Lift
dvVol_Living	= 2401:1:0		//NXC-VOL4
dvVol_Bed			= 2401:2:0		//NXC-VOL4
dvVol_Powder	= 2402:1:0		//NXC-VOL4
dvVol_Bath		= 2402:2:0		//NXC-VOL4
dvCurtainLR		= 5001:8:0		//Relay for Curtain
dvTP					= 10001:1:0		//MVP-8400
dvTP1b				= 10001:2:0		//MVP-8400	Used for TV Icon Dynamic Images

dvKP_Bath			= 128:1:0			//AXD-MSP8
dvKP_Powder		= 129:1:0			//AXD-MSP8


dvDebug				= 0:0:0  		  //telnet window

virtual				= 35100:1:0



(***********************************************************)
(*               CONSTANT DEFINITIONS GO BELOW             *)
(***********************************************************)
DEFINE_CONSTANT

SOURCE_TV				= 1
SOURCE_DVD			= 2
SOURCE_VOD			= 3
SOURCE_MUSIC		= 4
SOURCE_AUX			= 5
SOURCE_NONE			= 6
SOURCE_INTERNET	= 7

SOURCE_ON				= 1
SOURCE_OFF			= 2

DVD_PLAY = 1
DVD_STOP = 2
DVD_REW = 3
DVD_FWD = 4

TCP = 1
UDP = 2
TRUE = 1
FALSE = 0
RETRY_TIME = 10	//300;
IPCONN_DISCONNECTED = 0
IPCONN_CONNECTED = 1

CONST_LIVING = 0
CONST_BED = 1


// Switcher Levels
ALL_LEVEL	= 1		// '!'
AUDIO_LEVEL	= 2		// '$'
VIDEO_LEVEL	= 3		// '%'

MAX_LEVELS	= 3
MAX_OUTPUTS = 8

MAX_ICONS	= 95

(***********************************************************)
(*              DATA TYPE DEFINITIONS GO BELOW             *)
(***********************************************************)
DEFINE_TYPE

DEFINE_COMBINE
(***********************************************************)
(*               VARIABLE DEFINITIONS GO BELOW             *)
(***********************************************************)
DEFINE_VARIABLE

CHAR	strIconServerIP[] = '***********'
INTEGER	nIconIndex
CHAR	strIconFile[MAX_ICONS][64]


CHAR	strServerBuffer[65000]
CHAR	strXMLLine[255]

INTEGER	nSwitcherXpoints[MAX_LEVELS][MAX_OUTPUTS]

VOLATILE CHAR	strGuestName[64]
INTEGER nCheckWeather = 0
CHAR	strCurrentTime[8]
CHAR	strFireAlarmMessage_Bed[255]
CHAR	strFireAlarmMessage_Liv[255]

CHAR	strBedroomServerBuffer[1000]
CHAR	strBedroomMessage[255]
CHAR	strBedroomCommand[255]
CHAR	strLivroomServerBuffer[1000]
CHAR	strLivroomMessage[255]
CHAR	strLivingroomCommand[255]

CHAR	strCurrentTimeAndDate[32]

CHAR    strWeatherImageFile[128]
CHAR    strCurrentTemp[10]
CHAR    strCurrentTemp_C[3]
CHAR    strCurrentTemp_F[3]
FLOAT		fCurrentTemp;
INTEGER nTempMode = 1					// 1=F, 2=C
INTEGER nTempModeDisplay = 1			// 1=F, 2=C

INTEGER	nLivingroomVolumeLvl
INTEGER	nOldLivingroomVolumeLvl
INTEGER	nBedroomVolumeLvl
INTEGER	nOldBedroomVolumeLvl

//ferds - mar 17, 09
INTEGER	nLivingroomVolumeLvl01
INTEGER	nBedroomVolumeLvl01
INTEGER	ndvtp
//ferds - mar 17, 09

INTEGER	nLoadedLiving = 1
INTEGER	nLoadedBedroom = 1

CONSTANT CHAR cSTB_LivingAddress[] = '*********'
CONSTANT CHAR cSTB_BedAddress[] 	 = '*********'

CONSTANT LONG lServerPort = 8221


INTEGER nDND
INTEGER nDoor
INTEGER nSrcSel_Bed	= SOURCE_NONE
INTEGER nSrcSel_Living = SOURCE_NONE
INTEGER nSrcSel_Bath = SOURCE_DVD
INTEGER nSrcSel_Powder = SOURCE_DVD
INTEGER nLCDLivPwrStat
INTEGER nLCDBedPwrStat
INTEGER nLiftStat = 0
INTEGER nRoomSel


INTEGER nIPStatusLiving = IPCONN_DISCONNECTED
INTEGER nIPStatusBed = IPCONN_DISCONNECTED
INTEGER nVol_Living
INTEGER nVol_Bed
INTEGER nVol_Bath
INTEGER nVol_Powder
INTEGER nBRCurtainStat
INTEGER nLRCurtainStat
INTEGER nSrc_Bath = SOURCE_OFF
INTEGER nSrc_Bath_Trans
INTEGER nSrc_Powder = SOURCE_OFF
INTEGER nSrc_Powder_Trans


#INCLUDE 'RMSMain rev2.axi'
(***********************************************************)
(*               LATCHING DEFINITIONS GO BELOW             *)
(***********************************************************)
DEFINE_LATCHING

(***********************************************************)
(*       MUTUALLY EXCLUSIVE DEFINITIONS GO BELOW           *)
(***********************************************************)
DEFINE_MUTUALLY_EXCLUSIVE

(***********************************************************)
(*        SUBROUTINE/FUNCTION DEFINITIONS GO BELOW         *)
(***********************************************************)
/////////////////////////////////////////////////////////////
//initiate a TCP/IP connection
DEFINE_FUNCTION INTEGER fnTCP_ClientConnect(INTEGER nLivingOrBed)
{
  SLONG slResult

  SWITCH(nLivingOrBed)
	{
		CASE CONST_LIVING:
		{
			//first, ensure the port handle isnt open
			IF(nIPStatusLiving <> IPCONN_DISCONNECTED)
			{
				RETURN 0;  //this function goes no further
			}
			//attempt the connection
			SEND_STRING dvDebug, "'TRACE: fnTCP_ClientConnect(): LIVING: TCP Attempting connection to ',
			cSTB_LivingAddress,':',ITOA(lServerPort),'...'"  //debug
			slResult = IP_CLIENT_OPEN(dvSTB_Living.port,cSTB_LivingAddress,lServerPort,TCP)
			IF(slResult)
			{
				SELECT
				{
					ACTIVE(slResult == 1): { SEND_STRING dvDebug, "'TRACE: fnTCP_ClientConnect(): LIVING: invalid address [',cSTB_LivingAddress,']'" }
					ACTIVE(slResult == 2): { SEND_STRING dvDebug, "'TRACE: fnTCP_ClientConnect(): LIVING: invalid port [',itoa(lServerPort),']'" }
					ACTIVE(slResult == 3): { SEND_STRING dvDebug, "'TRACE: fnTCP_ClientConnect(): LIVING: invalid protocol'" }
					ACTIVE(slResult == 4): { SEND_STRING dvDebug, "'TRACE: fnTCP_ClientConnect(): LIVING: Unable to connect'" }
					ACTIVE(1):             { SEND_STRING dvDebug, "'TRACE: fnTCP_ClientConnect(): LIVING: TCP Connect Error: ',itoa(slResult)" }
				}
				RETURN 0
			}
			ELSE
			{
				//debug
				SEND_STRING dvDebug, "'TRACE: fnTCP_ClientConnect(): LIVING: ATTEMPTING TO CONNECT...'"

				RETURN 1
			}
		}
		CASE CONST_BED:
		{
			//first, ensure the port handle isnt open
			IF(nIPStatusBed <> IPCONN_DISCONNECTED)
			{
				RETURN 0;  //this function goes no further
			}

			//attempt the connection
			SEND_STRING dvDebug, "'TRACE: fnTCP_ClientConnect(): BED: TCP Attempting connection to ',
				cSTB_BedAddress,':',itoa(lServerPort),'...'"  //debug

			slResult = IP_CLIENT_OPEN(dvSTB_Bed.port,cSTB_BedAddress,lServerPort,TCP)
			IF(slResult)
			{
				SELECT
				{
					ACTIVE(slResult == 1): { SEND_STRING dvDebug, "'TRACE: fnTCP_ClientConnect(): BED: invalid address [',cSTB_BedAddress,']'" }
					ACTIVE(slResult == 2): { SEND_STRING dvDebug, "'TRACE: fnTCP_ClientConnect(): BED: invalid port [',itoa(lServerPort),']'" }
					ACTIVE(slResult == 3): { SEND_STRING dvDebug, "'TRACE: fnTCP_ClientConnect(): BED: invalid protocol'" }
					ACTIVE(slResult == 4): { SEND_STRING dvDebug, "'TRACE: fnTCP_ClientConnect(): BED: Unable to connect'" }
					ACTIVE(1):             { SEND_STRING dvDebug, "'TRACE: fnTCP_ClientConnect(): BED: TCP Connect Error: ',itoa(slResult)" }
				}
				RETURN 0
			}
			ELSE
			{
				//debug
				SEND_STRING dvDebug, "'TRACE: fnTCP_ClientConnect(): BED: ATTEMPTING TO CONNECT...'"

				RETURN 1
			}
		}
	}
}

/////////////////////////////////////////////////////////////
//maintain the IP connection
DEFINE_FUNCTION fnTCP_MaintainConnection()
{
  IF(nIPStatusLiving == IPCONN_DISCONNECTED)
  {
		fnTCP_ClientConnect(CONST_LIVING)
  }

  IF(nIPStatusBed == IPCONN_DISCONNECTED)
  {
		fnTCP_ClientConnect(CONST_BED)
  }
}


define_function fnParseServerMessage (CHAR strBuffer[], INTEGER nRoom)
STACK_VAR
  char 	strMsg[255]
  float fTemp
{
  if (nRoom == CONST_BED)
		send_string 0,"'Bedroom Server Data - ',strBuffer"
  else
		send_string 0,"'Living room Server Data - ',strBuffer"
	strMsg = remove_string(strBuffer,'#',1)

  // Global Data
  select
  {
		active (find_string(strMsg,'DND_ON',1)):
		{
			ON [dvDND,6]
			SEND_STRING dvSTB_Living,'$DON#'
			SEND_STRING dvSTB_Bed,'$DON#'
			nDND = 1
		}
		active (find_string(strMsg,'DND_OFF',1)):
		{
			OFF [dvDND,6]
			SEND_STRING dvSTB_Living,'$DOF#'
			SEND_STRING dvSTB_Bed,'$DOF#'
			nDND = 0
		}
		active (find_string(strMsg,'GUEST_NAME',1)):
		{
			set_length_string(strBuffer, length_string(strBuffer) - 2)	// remove 13,10
			strGuestName = strBuffer
			SEND_COMMAND dvTP,"'^BMF-1,0,%ML50,%T',strGuestName"
		}
		active (find_string(strMsg,'DATE',1)):
		{
			set_length_string(strBuffer, length_string(strBuffer) - 2)	// remove 13,10
			strCurrentTimeAndDate = strBuffer
			send_command 0,"'CLOCK ',strBuffer"
		}
		active(find_string(strMsg,'TEM',1)):
		{
			strWeatherImageFile = remove_string(strBuffer,',',1)
			set_length_string(strWeatherImageFile, length_string(strWeatherImageFile) - 1)
			strCurrentTemp	  = strBuffer

			if (find_string(strCurrentTemp,'F',1))
			{
				nTempMode = 1	// F
				if (!nTempModeDisplay)
					nTempModeDisplay = 1

				fCurrentTemp		= atof(strCurrentTemp)
				strCurrentTemp_F	= ftoa(fCurrentTemp)
				fTemp				= (fCurrentTemp - 32) / 1.8
				strCurrentTemp_C	= ftoa(fTemp)
			}
			else
			{
				nTempMode = 2	// C
				if (!nTempModeDisplay)
					nTempModeDisplay = 2

				fCurrentTemp		= atof(strCurrentTemp)
				strCurrentTemp_C	= ftoa(fCurrentTemp)
				fTemp				= (fCurrentTemp * 1.8) + 32

				strCurrentTemp_F	= ftoa(fTemp)
			}
			strCurrentTemp_C = "strCurrentTemp_C,'C'"
			strCurrentTemp_F = "strCurrentTemp_F,'F'"

			set_length_string(strCurrentTemp, length_string(strCurrentTemp) - 2)	// remove 13,10

			SEND_COMMAND dvTP,"'^RMF-weather_icon,%F',strWeatherImageFile"

			SEND_COMMAND dvTP,"'^TXT-5,1,',strCurrentTemp_F"
			SEND_COMMAND dvTP,"'^TXT-5,2,',strCurrentTemp_C"
		}
		ACTIVE(FIND_STRING(strMsg,'DOOR_OPEN',1)):
		{
			IF (nDoor = 1)
			{
				SEND_STRING dvSW,'5*12g'

				IF (nSrcSel_Living = SOURCE_TV)
					SEND_STRING dvSW,'1*1!'
				ELSE IF (nSrcSel_Living = SOURCE_DVD)
					SEND_STRING dvSW,'3*1!'
				ELSE IF (nSrcSel_Living = SOURCE_VOD)
					SEND_STRING dvSW,'1*1!'
				ELSE IF (nSrcSel_Living = SOURCE_MUSIC)
					SEND_STRING dvSW,'1*1$'
				ELSE IF (nSrcSel_Living = SOURCE_AUX)
					SEND_STRING dvSW,'4*1!'
				ELSE
					SEND_STRING dvSW,'1*1!'

				IF (nSrcSel_Bed = SOURCE_TV)
					SEND_STRING dvSW,'2*2!'
				ELSE IF (nSrcSel_Bed = SOURCE_DVD)
					SEND_STRING dvSW,'3*2!'
				ELSE IF (nSrcSel_Bed = SOURCE_VOD)
					SEND_STRING dvSW,'2*2!'
				ELSE IF (nSrcSel_Bed = SOURCE_MUSIC)
					SEND_STRING dvSW,'2*2$'
				ELSE IF (nSrcSel_Bed = SOURCE_AUX)
					SEND_STRING dvSW,'4*2!'
				ELSE
					SEND_STRING dvSW,'2*2!'

				IF (nSrcSel_Powder = SOURCE_TV)
					SEND_STRING dvSW,'1*4$'
				ELSE IF (nSrcSel_Powder = SOURCE_DVD)
					SEND_STRING dvSW,'3*4$'
				ELSE IF (nSrcSel_Powder = SOURCE_AUX)
					SEND_STRING dvSW,'4*4$'

				IF (nSrcSel_Bath = SOURCE_TV)
					SEND_STRING dvSW,'2*3$'
				ELSE IF (nSrcSel_Bath = SOURCE_DVD)
					SEND_STRING dvSW,'3*3$'
				ELSE IF (nSrcSel_Bath = SOURCE_AUX)
					SEND_STRING dvSW,'4*3$'

				CANCEL_WAIT 'Vol_On'
				PULSE [dvDoor,5]
				OFF[dvVol_Living,3]
				OFF[dvVol_Bed,3]
				OFF[dvVol_Bath,3]
				OFF[dvVol_Powder,3]
				SEND_STRING dvSTB_Living,'$DOR#'
				SEND_STRING dvSTB_Bed,'$DOR#'
				SEND_COMMAND dvTP,"'@PPK-PIP_Video'"
				IF (nSrc_Bath = SOURCE_OFF)
						SEND_STRING dvSW,'0*3$'
				IF (nSrc_Powder = SOURCE_OFF)
						SEND_STRING dvSW,'0*4$'
				IF (nLCDBedPwrStat = 2)
				{
					nLiftStat = 0
					nLCDBedPwrStat = 0
					cancel_wait 'Check LCDBed Power'
					SEND_STRING dvLCDBed,"'ka 1 0',13"
					PULSE[dvLCDBedIR,28]
					CANCEL_WAIT 'Lift_Up_On'
					CANCEL_WAIT 'Lift_Up_Off'
					OFF [dvLift,8]
					SEND_COMMAND dvLift_IO,'SET INPUT 4 LOW'
					WAIT 10 'Lift_Down' ON [dvLift_IO,4]
					WAIT 190 'Lift_Down' OFF [dvLift_IO,4]
					SEND_STRING dvSW,'0*2$'
					if (nIPStatusBed == ipconn_connected)
						SEND_STRING dvSTB_Bed,'$LOF#'
					else
						strBedroomCommand = '$LOF#'
				}
				IF (nLCDLivPwrStat = 2)
				{
					nLCDLivPwrStat = 0
					cancel_wait 'Check LCDLiv Power'
					PULSE[dvLCDLivIR,28]
					WAIT 7 PULSE[dvLCDLivIR,28]
					SEND_STRING dvLCDLiv,"'ka 1 0',13"
					SEND_STRING dvSW,'0*1$'
					if (nIPStatusLiving == ipconn_connected)
						SEND_STRING dvSTB_Living,'$LOF#'
					else
						strLivingroomCommand = '$LOF#'
				}
				nDoor = 0
			}
		}
	}
  switch (nRoom)
  {
		case CONST_BED:
		{
			select
			{
				active (find_string(strMsg,'ACK#',1)):
				{
					if (length_string(strBedroomCommand))
					{
					send_string dvSTB_Bed,"strBedroomCommand"
					}
					strBedroomCommand = ''
				}
				active (find_string(strMsg,'TV',1)):
				{
					IF (nSrcSel_Bed = SOURCE_DVD OR nSrcSel_Bed = SOURCE_AUX)
					{
						if (nIPStatusBed == ipconn_connected)
							SEND_STRING dvSTB_Bed,'$HOM#'
						else
							strBedroomCommand =  '$HOM#'
					}
					cancel_wait 'Check Switcher Connectivity'
					SEND_STRING dvSW,'2*2$'
					WAIT 20 SEND_STRING dvSW,'2*2$'
					nSrcSel_Bed = SOURCE_TV
					// Make sure TV is On and on right input
					if (nLCDBedPwrStat = 0)
					{
						cancel_wait 'Check LCDBed Power'
						SEND_STRING dvLCDBed,"'ka 1 1',13"
						PULSE[dvLCDBedIR,27]
						WAIT 60
						{
							cancel_wait 'Check LCDbed Power'
							SEND_STRING dvLCDBed,"'kb 1 60',13"
						}
						nLCDBedPwrStat = 1
					}
					// Make sure Lift is Up
					if (nLiftStat = 0)
					{
						CANCEL_WAIT 'Lift_Down_On'
						CANCEL_WAIT 'Lift_Down_Off'
						OFF [dvLift_IO,4]
						SEND_COMMAND dvLift_IO,'SET INPUT 4 HIGH'
						WAIT 10 'Lift_Up_On' ON [dvLift,8]
						WAIT 190 'Lift_Up_Off' OFF [dvLift,8]
						nLiftStat = 1
					}
				}
				active (find_string(strMsg,'DVD',1)):
				{
					IF (nSrcSel_Bed = SOURCE_TV OR nSrcSel_Bed = SOURCE_AUX)
					{
						if (nIPStatusBed == ipconn_connected)
							SEND_STRING dvSTB_Bed,'$HOM#'
						else
							strBedroomCommand = '$HOM#'
					}
					cancel_wait 'Check Switcher Connectivity'
					SEND_STRING dvSW,'3*2!'
					WAIT 20 SEND_STRING dvSW,'3*2!'
					nSrcSel_Bed = SOURCE_DVD
					// Make sure TV is On and on right input
					if (nLCDBedPwrStat = 0)
					{
						cancel_wait 'Check LCDBed Power'
						SEND_STRING dvLCDBed,"'ka 1 1',13"
						PULSE[dvLCDBedIR,27]
						WAIT 60
						{
							cancel_wait 'Check LCDbed Power'
							SEND_STRING dvLCDBed,"'kb 1 60',13"
						}
						nLCDBedPwrStat = 1
					}
					// Make sure Lift is Up
					if (nLiftStat = 0)
					{
						CANCEL_WAIT 'Lift_Down_On'
						CANCEL_WAIT 'Lift_Down_Off'
						OFF [dvLift_IO,4]
						SEND_COMMAND dvLift_IO,'SET INPUT 4 HIGH'
						WAIT 10 'Lift_Up_On' ON [dvLift,8]
						WAIT 190 'Lift_Up_Off' OFF [dvLift,8]
						nLiftStat = 1
					}
				}
				active (find_string(strMsg,'LCD',1)):
				{
					IF(nLCDBedPwrStat = 0)
					{
						cancel_wait 'Check LCDBed Power'
						SEND_STRING dvLCDBed,"'ka 1 1',13"
						PULSE[dvLCDBedIR,27]
						SEND_STRING dvSTB_Bed,'$LON#'
						WAIT 60
						{
							cancel_wait 'Check LCDBed Power'
							SEND_STRING dvLCDBed,"'kb 1 60',13"
							PULSE[dvLCDBedIR,91]
						}
						CANCEL_WAIT 'Lift_Down_On'
						CANCEL_WAIT 'Lift_Down_Off'
						OFF [dvLift_IO,4]
						SEND_COMMAND dvLift_IO,'SET INPUT 4 HIGH'
						WAIT 10 'Lift_Up_On' ON [dvLift,8]
						WAIT 190 'Lift_Up_Off' OFF [dvLift,8]
						nLiftStat = 1
						nLCDBedPwrStat = 1
					}
					ELSE
					{
						CANCEL_WAIT 'Check LCDBed Power'
						SEND_STRING dvLCDBed,"'ka 1 0',13"
						PULSE[dvLCDBedIR,28]
						SEND_STRING dvSTB_Bed,'$LOF#'
						SEND_STRING dvSW,'0*2$'
						CANCEL_WAIT 'Lift_Up_On'
						CANCEL_WAIT 'Lift_Up_Off'
						OFF [dvLift,8]
						SEND_COMMAND dvLift_IO,'SET INPUT 4 LOW'
						WAIT 10 'Lift_Down_On' ON [dvLift_IO,4]
						WAIT 190 'Lift_Down_Off' OFF [dvLift_IO,4]
						nLiftStat = 0
						nLCDBedPwrStat = 0
					}
				}
				active (find_string(strMsg,'VOD',1)):
				{
					cancel_wait 'Check Switcher Connectivity'
					SEND_STRING dvSW,'2*2$'
 					WAIT 20 SEND_STRING dvSW,'2*2$'
					nSrcSel_Bed = SOURCE_VOD
				}
				active (find_string(strMsg,'MUSIC',1)):
				{
					cancel_wait 'Check Switcher Connectivity'
					SEND_STRING dvSW,'2*2$'
 					WAIT 20 SEND_STRING dvSW,'2*2$'
				}
				active (find_string(strMsg,'AUX',1)):
				{
					IF (nSrcSel_Bed = SOURCE_DVD OR nSrcSel_Bed = SOURCE_TV)
					{
						if (nIPStatusBed == ipconn_connected)
							SEND_STRING dvSTB_Bed,'$HOM#'
						else
							strBedroomCommand =  '$HOM#'
					}
					cancel_wait 'Check Switcher Connectivity'
					SEND_STRING dvSW,'4*2!'
					WAIT 20 SEND_STRING dvSW,'4*2!'
					nSrcSel_Bed = SOURCE_AUX
					// Make sure TV is On and on right input
					if (nLCDBedPwrStat = 0)
					{
						cancel_wait 'Check LCDBed Power'
						SEND_STRING dvLCDBed,"'ka 1 1',13"
						PULSE[dvLCDBedIR,27]
						WAIT 60
						{
							cancel_wait 'Check LCDbed Power'
							SEND_STRING dvLCDBed,"'kb 1 60',13"
							PULSE[dvLCDBedIR,91]
						}
						nLCDBedPwrStat = 1
					}
					// Make sure Lift is Up
					if (nLiftStat = 0)
					{
						CANCEL_WAIT 'Lift_Down_On'
						CANCEL_WAIT 'Lift_Down_Off'
						OFF [dvLift_IO,4]
						SEND_COMMAND dvLift_IO,'SET INPUT 4 HIGH'
						WAIT 10 'Lift_Up_On' ON [dvLift,8]
						WAIT 190 'Lift_Up_Off' OFF [dvLift,8]
						nLiftStat = 1
					}
				}
				ACTIVE(FIND_STRING(strMsg,'CURTAIN_OPEN',1)):
				{
					PULSE [dvCurtainBR,1]
				}
				ACTIVE(FIND_STRING(strMsg,'CURTAIN_CLOSE',1)):
				{
					PULSE [dvCurtainBR,2]
				}
				ACTIVE(FIND_STRING(strMsg,'VOL_UP',1)):
				{
					OFF[dvVol_Bed,3]
					PULSE[dvVol_Bed,1]
					nVol_Bed = 0
				}
				ACTIVE(FIND_STRING(strMsg,'VOL_DOWN',1)):
				{
					OFF[dvVol_Bed,3]
					PULSE[dvVol_Bed,2]
					nVol_Bed = 0
				}
				ACTIVE(FIND_STRING(strMsg,'VOL_MUTE',1)):
				{
					[dvVol_Bed,3] = ![dvVol_Bed,3]
				}
				ACTIVE(FIND_STRING(strMsg,'MUTE',1)):
				{
					ON[dvVol_Bed,3]
				}
				ACTIVE(FIND_STRING(strMsg,'UNMUTE',1)):
				{
					WAIT 20 OFF[dvVol_Bed,3]
				}
				ACTIVE(FIND_STRING(strMsg,'PLAY',1)):
				{
					PULSE [dvDVD,1]
				}
				ACTIVE(FIND_STRING(strMsg,'STOP',1)):
				{
					PULSE [dvDVD,2]
				}
				ACTIVE(FIND_STRING(strMsg,'PAUSE',1)):
				{
					PULSE [dvDVD,3]
				}
				ACTIVE(FIND_STRING(strMsg,'NEXT',1)):
				{
					PULSE [dvDVD,4]
				}
				ACTIVE(FIND_STRING(strMsg,'PREV',1)):
				{
					PULSE [dvDVD,5]
				}
				ACTIVE(FIND_STRING(strMsg,'FWD',1)):
				{
					PULSE [dvDVD,6]
				}
				ACTIVE(FIND_STRING(strMsg,'REV',1)):
				{
					PULSE [dvDVD,7]
				}
				ACTIVE(FIND_STRING(strMsg,'ETR',1)):
				{
					SEND_COMMAND dvTP,"'PAGE-Bed_AV_Blank'"
				}
				ACTIVE(FIND_STRING(strMsg,'HOM',1)):
				{
					cancel_wait 'Check Switcher Connectivity'
					SEND_STRING dvSW,'0*2$'
					WAIT 20 SEND_STRING dvSW,'0*2$'
					nSrcSel_Bed = SOURCE_NONE
				}
			}
		}
		case CONST_LIVING:
		{
			select
			{
				active (find_string(strMsg,'ACK#',1)) :
				{
					if (length_string(strLivingroomCommand))
					{
						send_string dvSTB_Living,"strLivingroomCommand";
					}
					strLivingroomCommand = ''
				}
				ACTIVE(FIND_STRING(strMsg,'CURTAIN_OPEN',1)):
				{
					PULSE [dvCurtainLR,3]
				}
				ACTIVE(FIND_STRING(strMsg,'CURTAIN_CLOSE',1)):
				{
					IF(nLRCurtainStat = 1)
					{
						PULSE [dvCurtainLR,4]
						nLRCurtainStat = 0
					}
					ELSE
					{
						nLRCurtainStat = 1
					}
				}
				ACTIVE(FIND_STRING(strMsg,'VOL_UP',1)):
				{
					OFF[dvVol_Living,3]
					PULSE[dvVol_Living,1]
					nVol_Living = 0
				}
				ACTIVE(FIND_STRING(strMsg,'VOL_DOWN',1)):
				{
					OFF[dvVol_Living,3]
					PULSE[dvVol_Living,2]
					nVol_Living = 0
				}
				ACTIVE(FIND_STRING(strMsg,'VOL_MUTE',1)):
				{
					[dvVol_Living,3] = ![dvVol_Living,3]
				}
				ACTIVE(FIND_STRING(strMsg,'MUTE',1)):
				{
					ON[dvVol_Living,3]
				}
				ACTIVE(FIND_STRING(strMsg,'UNMUTE',1)):
				{
					WAIT 20 OFF[dvVol_Living,3]
				}
				ACTIVE(FIND_STRING(strMsg,'PLAY',1)):
				{
					PULSE [dvDVD,1]
				}
				ACTIVE(FIND_STRING(strMsg,'STOP',1)):
				{
					PULSE [dvDVD,2]
				}
				ACTIVE(FIND_STRING(strMsg,'PAUSE',1)):
				{
					PULSE [dvDVD,3]
				}
				ACTIVE(FIND_STRING(strMsg,'NEXT',1)):
				{
					PULSE [dvDVD,4]
				}
				ACTIVE(FIND_STRING(strMsg,'PREV',1)):
				{
					PULSE [dvDVD,5]
				}
				ACTIVE(FIND_STRING(strMsg,'FWD',1)):
				{
					PULSE [dvDVD,6]
				}
				ACTIVE(FIND_STRING(strMsg,'REV',1)):
				{
					PULSE [dvDVD,7]
				}
				ACTIVE(FIND_STRING(strMsg,'TV',1)):
				{
					IF (nSrcSel_Living = SOURCE_DVD OR nSrcSel_Living = SOURCE_AUX)
					{
						if (nIPStatusLiving == ipconn_connected)
							SEND_STRING dvSTB_Living,'$HOM#'
						else
							strLivingroomCommand = '$HOM#'
					}
					cancel_wait 'Check Switcher Connectivity'
					SEND_STRING dvSW,'1*1$'
					WAIT 20 SEND_STRING dvSW,'1*1$'
					nSrcSel_Living = SOURCE_TV
					// Make sure TV is On and on right input
					if (nLCDLivPwrStat = 0)
					{
						cancel_wait 'Check LCDLiv Power'
						SEND_STRING dvLCDLiv,"'ka 1 1',13"
						PULSE[dvLCDLivIR,27]
						WAIT 7 PULSE[dvLCDLivIR,27]
						WAIT 60
						{
						cancel_wait 'Check LCDLiv Power'
						SEND_STRING dvLCDLiv,"'xb 1 60',13"
						PULSE[dvLCDLivIR,29]
						}
						nLCDLivPwrStat = 1
					}
				}
				ACTIVE(FIND_STRING(strMsg,'DVD',1)):
				{
					IF (nSrcSel_Living = SOURCE_TV OR nSrcSel_Living = SOURCE_AUX)
					{
						if (nIPStatusLiving == ipconn_connected)
							SEND_STRING dvSTB_Living,'$HOM#'
						else
							strLivingroomCommand = '$HOM#'
					}
					cancel_wait 'Check Switcher Connectivity'
					SEND_STRING dvSW,'3*1!'
					WAIT 20 SEND_STRING dvSW,'3*1!'
					nSrcSel_Living = SOURCE_DVD
					// Make sure TV is On and on right input
					if (nLCDLivPwrStat = 0)
					{
						cancel_wait 'Check LCDLiv Power'
						SEND_STRING dvLCDLiv,"'ka 1 1',13"
						PULSE[dvLCDLivIR,27]
						WAIT 7 PULSE[dvLCDLivIR,27]
						WAIT 60
						{
							cancel_wait 'Check LCDLiv Power'
						  SEND_STRING dvLCDLiv,"'xb 1 60',13"
							PULSE[dvLCDLivIR,29]
						}
						nLCDLivPwrStat = 1
					}
				}
				ACTIVE (find_string(strMsg,'LCD',1)):
				{
					IF(nLCDLivPwrStat = 0)
					{
						cancel_wait 'Check LCDLiv Power'
						SEND_STRING dvLCDLiv,"'ka 1 1',13"
						PULSE[dvLCDLivIR,27]
						WAIT 7 PULSE[dvLCDLivIR,27]
						SEND_STRING dvSTB_Living,'$LON#'
						WAIT 60
						{
							cancel_wait 'Check LCDLiv Power'
							SEND_STRING dvLCDLiv,"'xb 1 60',13"
							PULSE[dvLCDLivIR,29]
						}
					nLCDLivPwrStat = 1
					}
					ELSE
					{
						cancel_wait 'Check LCDLiv Power'
						SEND_STRING dvLCDLiv,"'ka 1 0',13"
						PULSE[dvLCDLivIR,28]
						WAIT 7 PULSE[dvLCDLivIR,28]
						SEND_STRING dvSTB_Living,'$LOF#'
						SEND_STRING dvSW,'0*1$'
						nLCDLivPwrStat = 0
					}
				}
				ACTIVE(FIND_STRING(strMsg,'VOD',1)):
				{
					cancel_wait 'Check Switcher Connectivity'
					SEND_STRING dvSW,'1*1$'
 					WAIT 20 SEND_STRING dvSW,'1*1$'
					nSrcSel_Living = SOURCE_VOD
				}
				ACTIVE(FIND_STRING(strMsg,'MUSIC',1)):
				{
					cancel_wait 'Check Switcher Connectivity'
					SEND_STRING dvSW,'1*1$'
 					WAIT 20 SEND_STRING dvSW,'1*1$'
					nSrcSel_Living = SOURCE_MUSIC
				}
				ACTIVE(FIND_STRING(strMsg,'AUX',1)):
				{
					IF (nSrcSel_Living = SOURCE_TV OR nSrcSel_Living = SOURCE_DVD)
					{
						if (nIPStatusLiving == ipconn_connected)
							SEND_STRING dvSTB_Living,'$HOM#'
						else
							strLivingroomCommand = '$HOM#'
					}
					cancel_wait 'Check Switcher Connectivity'
					SEND_STRING dvSW,'4*1!'
					WAIT 20 SEND_STRING dvSW,'4*1!'
					nSrcSel_Living = SOURCE_AUX
					// Make sure TV is On and on right input
					if (nLCDLivPwrStat = 0)
					{
						cancel_wait 'Check LCDLiv Power'
						SEND_STRING dvLCDLiv,"'ka 1 1',13"
						PULSE[dvLCDLivIR,27]
						WAIT 7 PULSE[dvLCDLivIR,27]
						WAIT 60
						{
						cancel_wait 'Check LCDLiv Power'
						SEND_STRING dvLCDLiv,"'xb 1 60',13"
						PULSE[dvLCDLivIR,29]
						}
						nLCDLivPwrStat = 1
					}
				}
				ACTIVE(FIND_STRING(strMsg,'HOM',1)):
				{
					cancel_wait 'Check Switcher Connectivity'
					SEND_STRING dvSW,'0*1$'
					WAIT 20 SEND_STRING dvSW,'0*1$'
					nSrcSel_Living = SOURCE_NONE
				}
				ACTIVE(FIND_STRING(strMsg,'ETR',1)):
				{
				}
			}
		}
  }
}

(***********************************************************)
(*                STARTUP CODE GOES BELOW                  *)
(***********************************************************)
DEFINE_START

create_buffer dvServer,strServerBuffer
create_buffer dvSTB_Bed,strBedroomServerBuffer
create_buffer dvSTB_Living,strLivroomServerBuffer

/////////////////////////////////////////////////////////////
//after a soft reboot, tell the AMX that the port is closed
nIPStatusLiving = IPCONN_DISCONNECTED
nIPStatusBed = IPCONN_DISCONNECTED


WAIT 80 SEND_STRING dvSW,'1*6G'
WAIT 82 SEND_STRING dvSW,'2*6G'
WAIT 84 SEND_STRING dvSW,'3*6g'
WAIT 86 SEND_STRING dvSW,'4*6g'
WAIT 88 SEND_STRING dvSW,'5*6G'
WAIT 90 SEND_STRING dvSW,'6*6G'
WAIT 80 PULSE [dvDVD,2]

(***********************************************************)
(*                THE EVENTS GO BELOW                      *)
(***********************************************************)
DEFINE_EVENT

data_event[dvServer]
{
  online:
  {
		send_string 0,'Main Server Online'
		SEND_STRING data.device,"'GET /images/tv/tvchannel.xml',10,13,10,13";
  }
  string:
  {
  }
  offline:
  {
		send_string 0,'Main Server Offline-Parse XML File'
		send_string 0,"'buffer size=',itoa(length_string(strServerBuffer))"
	// all this is done in Mainline (DEFINE_PROGRAM) section
  }
  onerror:
  {
		 /*
			2: General failure (out of memory)
			4: Unknown host
			6: Connection refused
			7: Connection timed out
			8: Unknown connection error
			14: Local port already used
			16: Too many open sockets
			17: Local Port Not Open
		*/
		send_string 0,"'Main Server Error-',data.number"
  }
}

data_event[0:1:0]
{
  online:
  {
		nLoadedLiving = 0
		nLoadedBedroom= 0
  }
}

DATA_EVENT[dvSW]
{
  ONLINE:
  {
		SEND_COMMAND dvSW, 'SET BAUD 9600, N, 8, 1, 485 DISABLE'
  }
  STRING:
  {
		char	strMsg[255]
		char	strOut[10]
		integer nOutput
		char	strIn[10]
		integer nInput
		char	strLevel[10]
		strMsg = remove_string(data.text,"13,10",1)

		if (length_string(strMsg))
		{
			set_length_string(strMsg, length_string(strMsg) - 2)	// remove 13,10
			select
			{
			// Syntax : Input 1 to Output 3 Audio level returns:
			// Out3 In1 Aud
				active (find_string(strMsg,'Out',1) && find_string(strMsg,'In',1)):
				{
					strOut 	= remove_string(strMsg,' ',1)	// removes 'Out3 '
					nOutput	= atoi(strOut);
					strIn  	= remove_string(strMsg,' ',1) 	// removes 'In1 '
					nInput	= atoi(strIn);
					strLevel	= strMsg;						// removes 'Aud'
					select
					{
						active (find_string(strLevel,'Aud',1)) :
						{
							nSwitcherXpoints[AUDIO_LEVEL][nOutput] = nInput
						}
						active (find_string(strLevel,'All',1)) :
						{
							nSwitcherXpoints[ALL_LEVEL][nOutput] = nInput
						}
						active (find_string(strLevel,'Vid',1)) :
						{
							nSwitcherXpoints[VIDEO_LEVEL][nOutput] = nInput
						}
					}
				}
			}
		}
  }
}
DATA_EVENT[dvDVD]
{
	ONLINE:
	{
		SEND_COMMAND dvDVD, 'SET MODE IR'
		SEND_COMMAND dvDVD, 'CARON'
	}
}
DATA_EVENT[dvLCDBedIR]
{
	ONLINE:
	{
		SEND_COMMAND dvLCDBedIR, 'SET MODE IR'
		SEND_COMMAND dvLCDBedIR, 'CARON'
	}
}
DATA_EVENT[dvLCDLivIR]
{
	ONLINE:
	{
		SEND_COMMAND dvLCDLivIR, 'SET MODE IR'
		SEND_COMMAND dvLCDLivIR, 'CARON'
	}
}
DATA_EVENT[dvLCDBed]
{
  ONLINE:
  {
		SEND_COMMAND dvLCDBed, 'SET BAUD 9600, N, 8, 1, 485 DISABLE'
		wait 10
			send_string dvLCDBed,"'ka 1 FF',13"
  }
  STRING:
  {
	// Parse message
		IF (nDoor = 1)
		{
			IF (nLCDBedPwrStat = 0)
			{
				select
				{
					active (find_string(upper_string(data.text),'A 01 OK01X',1)) :	// LCD ON
					{
						#IF_DEFINED vdvRMSEngine
							RMSSetDevicePower(data.device, 1)
						#END_IF
						nLCDBedPwrStat = 2
						nLiftStat = 1
					}
					active (find_string(upper_string(data.text),'A 01 OK00X',1)) :	// LCD OFF
					{
						#IF_DEFINED vdvRMSEngine
							RMSSetDevicePower(data.device, 0)
						#END_IF
						nLCDBedPwrStat = 0
						nLiftStat = 0
						if (nIPStatusBed == ipconn_connected)
							SEND_STRING dvSTB_Bed,'$LOF#'
						else
							strBedroomCommand = '$LOF#'
					}
				}
			}
		}
		ELSE
		{
			select
			{
				active (find_string(upper_string(data.text),'A 01 OK01X',1)) :	// LCD ON
				{
					#IF_DEFINED vdvRMSEngine
						RMSSetDevicePower(data.device, 1)
					#END_IF
					nLCDBedPwrStat = 1
					nLiftStat = 1
				}
				active (find_string(upper_string(data.text),'A 01 OK00X',1)) :	// LCD OFF
				{
					#IF_DEFINED vdvRMSEngine
						RMSSetDevicePower(data.device, 0)
					#END_IF
					nLCDBedPwrStat = 0
					nLiftStat = 0
					if (nIPStatusBed == ipconn_connected)
						SEND_STRING dvSTB_Bed,'$LOF#'
					else
						strBedroomCommand = '$LOF#'
				}
			}
		}
  }
}
DATA_EVENT[dvLCDLiv]
{
  ONLINE:
  {
		SEND_COMMAND dvLCDLiv, 'SET BAUD 9600, N, 8, 1, 485 DISABLE'
		wait 10
			send_string dvLCDLiv,"'ka 1 FF',13"
  }
  STRING:
  {
	// Parse message
		IF (nDoor = 1)
		{
			IF (nLCDLivPwrStat = 0)
			{
				select
				{
					active (find_string(upper_string(data.text),'A 01 OK01X',1)) :	// LCD ON
					{
						#IF_DEFINED vdvRMSEngine
							RMSSetDevicePower(data.device, 1)
						#END_IF
						nLCDLivPwrStat = 2
					}
					active (find_string(upper_string(data.text),'A 01 OK00X',1)) :	// LCD OFF
					{
						#IF_DEFINED vdvRMSEngine
							RMSSetDevicePower(data.device, 0)
						#END_IF
						nLCDLivPwrStat = 0
						if (nIPStatusLiving == ipconn_connected)
							SEND_STRING dvSTB_Living,'$LOF#'
						else
							strLivingroomCommand = '$LOF#'
					}
				}
			}
		}
		ELSE
		{
			select
			{
				active (find_string(upper_string(data.text),'A 01 OK01X',1)) :	// LCD ON
				{
					#IF_DEFINED vdvRMSEngine
						RMSSetDevicePower(data.device, 1)
					#END_IF
					nLCDLivPwrStat = 1
				}
				active (find_string(upper_string(data.text),'A 01 OK00X',1)) :	// LCD OFF
				{
					#IF_DEFINED vdvRMSEngine
						RMSSetDevicePower(data.device, 0)
					#END_IF
					nLCDLivPwrStat = 0
					if (nIPStatusLiving == ipconn_connected)
						SEND_STRING dvSTB_Living,'$LOF#'
					else
						strLivingroomCommand = '$LOF#'
				}
			}
		}
  }
}

DATA_EVENT[dvSTB_Living]
{
  ONLINE:
  {
	//RMSSetLivServerDevice_Communicating(1);

	SEND_COMMAND vdvRMSEngine,'SET PARAM-0:4:0,Device Communicating,1'

	send_string 0,"'living server online - inform RMS'";

		nIPStatusLiving = IPCONN_CONNECTED
    CANCEL_WAIT 'Reconnect Living Server'
    //debug
    SEND_STRING dvDebug, "'TRACE: fnTCP_ClientConnect(): NOW CONNECTED TO LIVING...'"
		if (nLoadedLiving == 0)
		{
			wait 30
			send_string dvSTB_Living,'$TEM#'
		}
		else
		{
			if (length_string(strLivingroomCommand) == 0 && length_string(strFireAlarmMessage_Liv) == 0)
			SEND_STRING data.device,'$ACO#'

			if (length_string(strLivingroomCommand))
			send_string data.device,"strLivingroomCommand"

			if (length_string(strFireAlarmMessage_Liv))
			SEND_STRING data.device,"strFireAlarmMessage_Liv"
		}
	nLoadedLiving = 1
	strLivingroomCommand = ''
	strFireAlarmMessage_Liv = ''
  }
  STRING:
  {
	send_string 0,"'living server online - inform RMS'";
	SEND_COMMAND vdvRMSEngine,'SET PARAM-0:4:0,Device Communicating,1'
  }
  OFFLINE:
  {
	nRMSLivServerDevice_Communicating = 0;
	//RMSSetLivServerDevice_Communicating(0);
	SEND_COMMAND vdvRMSEngine,'SET PARAM-0:4:0,Device Communicating,0'
	send_string 0,"'living server offline - inform RMS'";
		//IP_CLIENT_CLOSE(dvSTB_Living.port)
		nIPStatusLiving = IPCONN_DISCONNECTED
  }
  ONERROR:
  {
   /*
	  2: General failure (out of memory)
	  4: Unknown host
	  6: Connection refused
	  7: Connection timed out
	  8: Unknown connection error
	  14: Local port already used
	  16: Too many open sockets
	  17: Local Port Not Open
	*/
		SWITCH(DATA.NUMBER)
		{
			CASE 9:     // Socket closed in response to IP_CLIENT_CLOSE
			CASE 17:    // String was sent to a closed socket
			{
				WAIT RETRY_TIME 'Reconnect Living Server'
					IF (nIPStatusLiving == ipconn_disconnected)
						IP_CLIENT_OPEN(dvSTB_Living.port,cSTB_LivingAddress,lServerPort,TCP)
			}
			DEFAULT:   // All other errors.  May want to re-try connection
			{
				WAIT RETRY_TIME 'Reconnect Living Server'
					IF (nIPStatusLiving == ipconn_disconnected)
						IP_CLIENT_OPEN(dvSTB_Living.port,cSTB_LivingAddress,lServerPort,TCP)
			}
		}
  }
}

DATA_EVENT[dvSTB_Bed]
{
  ONLINE:
  {
	//RMSSetBedServerDevice_Communicating(1);
	SEND_COMMAND vdvRMSEngine,'SET PARAM-0:3:0,Device Communicating,1'

	send_string 0,"'bedroom server online - inform RMS'";

		nIPStatusBed = IPCONN_CONNECTED;
		CANCEL_WAIT 'Reconnect Bed Server'
		//debug
		SEND_STRING dvDebug, "'TRACE: fnTCP_ClientConnect(): NOW CONNECTED TO BED...'"
		if (nLoadedBedroom == 0)
		{
			wait 30
			send_string dvSTB_Bed,'$TEM#'
		}
		else
		{
			if (length_string(strBedroomCommand) == 0 && length_string(strFireAlarmMessage_Bed) == 0)
			SEND_STRING data.device,'$ACO#'

			if (length_string(strBedroomCommand))
			send_string data.device,"strBedroomCommand"

			if (length_string(strFireAlarmMessage_Bed))
			SEND_STRING data.device,"strFireAlarmMessage_Bed"
		}
	nLoadedBedroom = 1
	strBedroomCommand = ''
	strFireAlarmMessage_Bed = ''
  }
  STRING:
  {
	SEND_COMMAND vdvRMSEngine,'SET PARAM-0:3:0,Device Communicating,1'
	send_string 0,"'bedroom server online - inform RMS'";
  }
  OFFLINE:
  {
	//RMSSetBedServerDevice_Communicating(0);
	SEND_COMMAND vdvRMSEngine,'SET PARAM-0:3:0,Device Communicating,0'
	send_string 0,"'bedroom server offline - inform RMS'";
	nRMSBedServerDevice_Communicating = 0;
		//IP_CLIENT_CLOSE(dvSTB_Bed.port)
		nIPStatusBed = IPCONN_DISCONNECTED
  }
  ONERROR:
  {
   /*
	  2: General failure (out of memory)
	  4: Unknown host
	  6: Connection refused
	  7: Connection timed out
	  8: Unknown connection error
	  14: Local port already used
	  16: Too many open sockets
	  17: Local Port Not Open
	*/
		SWITCH(DATA.NUMBER)
		{
			CASE 9:     // Socket closed in response to IP_CLIENT_CLOSE
			CASE 17:    // String was sent to a closed socket
			{
				WAIT RETRY_TIME 'Reconnect Bed Server'
					IF (nIPStatusBed == ipconn_disconnected)
						IP_CLIENT_OPEN(dvSTB_Bed.port,cSTB_BedAddress,lServerPort,TCP)
			}
			DEFAULT:   // All other errors.  May want to re-try connection
			{
				WAIT RETRY_TIME 'Reconnect Bed Server'
					IF (nIPStatusBed == ipconn_disconnected)
						IP_CLIENT_OPEN(dvSTB_Bed.port,cSTB_BedAddress,lServerPort,TCP)
			}
		}
  }
}


data_event[dvTP]
{
  ONLINE:
  {
		SEND_COMMAND data.device,'PAGE-WELCOME'
		SEND_COMMAND data.device,'@PPX'
		SEND_COMMAND data.device,'TPAGEON'
//ferds - mar 17, 09
		ndvtp = ipconn_connected
//ferds - mar 17, 09
		// Update Information
		wait 20
		{
			SEND_COMMAND dvTP,"'^BMF-1,0,%ML50,%T',strGuestName"

			SEND_COMMAND dvTP,"'^RMF-weather_icon,%F',strWeatherImageFile"

			SEND_COMMAND dvTP,"'^TXT-5,1,',strCurrentTemp_F"
			SEND_COMMAND dvTP,"'^TXT-5,2,',strCurrentTemp_C"

			// Get the Latest TV Channel Icon List
			IP_CLIENT_OPEN(dvServer.port,strIconServerIP,80,TCP)
		}
  }
  STRING:
  {
		select
		{
			active(find_string(data.text,'PAGE-',1)):
			{
			if (find_string(data.text,'BR -',1))
				nRoomSel = 2
			else if (find_string(data.text,'LR -',1))
				nRoomSel = 1
			}
		}
  }
//ferds - mar 17, 09
	OFFLINE:
	{
	ndvtp = ipconn_disconnected
	WAIT 2
		{
		  IF(nLivingroomVolumeLvl < nLivingroomVolumeLvl01)
				{
						off[dvVol_Living,3]
						SEND_COMMAND dvVol_Living,"'P0L',itoa(nLivingroomVolumeLvl01)"
				}
		}
	WAIT 3
		{
		  IF(nBedroomVolumeLvl < nBedroomVolumeLvl01)
				{
						off[dvVol_Bed,3]
						SEND_COMMAND dvVol_Bed,"'P0L',itoa(nBedroomVolumeLvl01)"
				}
		}
	}
//ferds - mar 17, 09
}


data_event[dvAlarm_IO]
{
  ONLINE:
  {
		SEND_COMMAND DATA.DEVICE,'SET INPUT 1 LOW'
  }
}
data_event[dvBell_IO]
{
  ONLINE:
  {
		SEND_COMMAND DATA.DEVICE,'SET INPUT 3 HIGH'
  }
}

(***********************************************************)
(*                FOR MVP-8400 TOUCH PANEL                 *)
(***********************************************************)
BUTTON_EVENT[dvTP,1]          //TV Select
{
  PUSH:
  {
		SWITCH (nRoomSel)
		{
			CASE 1:
			{
				IF ((nSrcSel_Living != SOURCE_TV))
				{
					if (nIPStatusLiving == ipconn_connected)
						SEND_STRING dvSTB_Living,'$HOM#'
					else
						strLivingroomCommand = '$HOM#'
				}
				WAIT 20	'Command To Living Server'
				{
					if (nIPStatusLiving == ipconn_connected)
						SEND_STRING dvSTB_Living,'$TV#'
					else
						strLivingroomCommand = '$TV#'
				}
				WAIT 30
				{
					cancel_wait 'Check Switcher Connectivity'
					SEND_STRING dvSW,'1*1$'
					nSrcSel_Living = SOURCE_TV
				}
				WAIT 40 SEND_STRING dvSW,'1*1$'
				// Make sure TV is On and on right input
				if (nLCDLivPwrStat = 0)
				{
					cancel_wait 'Check LCDLiv Power'
					SEND_STRING dvLCDLiv,"'ka 1 1',13"
					PULSE[dvLCDLivIR,27]
					WAIT 7 PULSE[dvLCDLivIR,27]
					WAIT 60
					{
						cancel_wait 'Check LCDLiv Power'
						SEND_STRING dvLCDLiv,"'xb 1 60',13"
						PULSE[dvLCDLivIR,29]
					}
					nLCDLivPwrStat = 1
				}
			}
			CASE 2:
			{
				IF ((nSrcSel_Bed != SOURCE_TV))
				{
					if (nIPStatusBed == ipconn_connected)
						SEND_STRING dvSTB_Bed,'$HOM#'
					else
						strBedroomCommand =  '$HOM#'
				}
				WAIT 20 'Command To Bed Server'
				{
					if (nIPStatusBed == ipconn_connected)
						send_string dvSTB_Bed,'$TV#'
					else
						strBedroomCommand = '$TV#'
				}
				WAIT 30
				{
					cancel_wait 'Check Switcher Connectivity'
					SEND_STRING dvSW,'2*2$'
					nSrcSel_Bed = SOURCE_TV
				}
				WAIT 40 SEND_STRING dvSW,'2*2$'
				// Make sure TV is On and on right input
				if (nLCDBedPwrStat = 0)
				{
					cancel_wait 'Check LCDBed Power'
					SEND_STRING dvLCDBed,"'ka 1 1',13"
					PULSE[dvLCDBedIR,27]
					WAIT 60
					{
						cancel_wait 'Check LCDbed Power'
						SEND_STRING dvLCDBed,"'kb 1 60',13"
						PULSE[dvLCDBedIR,29]
					}
					nLCDBedPwrStat = 1
				}
				// Make sure Lift is Up
				if (nLiftStat = 0)
				{
					CANCEL_WAIT 'Lift_Down_On'
					CANCEL_WAIT 'Lift_Down_Off'
					OFF [dvLift_IO,4]
					WAIT 5 SEND_COMMAND dvLift_IO,'SET INPUT 4 HIGH'
					WAIT 10 'Lift_Up_On' ON [dvLift,8]
					WAIT 190 'Lift_Up_Off' OFF [dvLift,8]
					nLiftStat = 1
				}
			}
		}
	}
}
BUTTON_EVENT[dvTP,2]          //DVD Select
{
  PUSH:
  {
		SWITCH (nRoomSel)
		{
			CASE 1:
			{
				IF ((nSrcSel_Living != SOURCE_DVD))
				{
					if (nIPStatusLiving == ipconn_connected)
						SEND_STRING dvSTB_Living,'$HOM#'
					else
						strLivingroomCommand = '$HOM#'
				}
				WAIT 20	'Command To Living Server'
				{
					if (nIPStatusLiving == ipconn_connected)
						SEND_STRING dvSTB_Living,'$DVD#'
					else
						strLivingroomCommand = '$DVD#'
				}
				WAIT 30
				{
					nSrcSel_Living= SOURCE_DVD
					cancel_wait 'Check Switcher Connectivity'
					SEND_STRING dvSW,'3*1!'
				}
				WAIT 40 SEND_STRING dvSW,'3*1!'
				// Make sure TV is On and on right input
				if (nLCDLivPwrStat = 0)
				{
					cancel_wait 'Check LCDLiv Power'
					SEND_STRING dvLCDLiv,"'ka 1 1',13"
					PULSE[dvLCDLivIR,27]
					WAIT 7 PULSE[dvLCDLivIR,27]
					WAIT 60
					{
						cancel_wait 'Check LCDLiv Power'
						SEND_STRING dvLCDLiv,"'xb 1 60',13"
						PULSE[dvLCDLivIR,29]
					}
					nLCDLivPwrStat = 1
				}
			}
			CASE 2:
			{
				IF ((nSrcSel_Bed != SOURCE_DVD))
				{
					if (nIPStatusBed == ipconn_connected)
						SEND_STRING dvSTB_Bed,'$HOM#'
					else
						strBedroomCommand = '$HOM#'
				}
				WAIT 20 'Command To Bed Server'
				{
					if (nIPStatusBed == ipconn_connected)
						send_string dvSTB_Bed,'$DVD#'
					else
						strBedroomCommand = '$DVD#'
				}
				WAIT 30
				{
					nSrcSel_Bed= SOURCE_DVD
					cancel_wait 'Check Switcher Connectivity'
					SEND_STRING dvSW,'3*2!'
				}
				WAIT 40 SEND_STRING dvSW,'3*2!'
				// Make sure TV is On and on right input
				if (nLCDBedPwrStat = 0)
				{
					cancel_wait 'Check LCDBed Power'
					SEND_STRING dvLCDBed,"'ka 1 1',13"
					PULSE[dvLCDBedIR,27]
					WAIT 60
					{
						cancel_wait 'Check LCDbed Power'
						SEND_STRING dvLCDBed,"'kb 1 60',13"
						PULSE[dvLCDBedIR,29]
					}
					nLCDBedPwrStat = 1
				}
				// Make sure Lift is Up
				if (nLiftStat = 0)
				{
					CANCEL_WAIT 'Lift_Down_On'
					CANCEL_WAIT 'Lift_Down_Off'
					OFF [dvLift_IO,4]
					WAIT 5 SEND_COMMAND dvLift_IO,'SET INPUT 4 HIGH'
					WAIT 10 'Lift_Up_On' ON [dvLift,8]
					WAIT 190 'Lift_Up_Off' OFF [dvLift,8]
					nLiftStat = 1
				}
			}
		}
  }
}
BUTTON_EVENT[dvTP,3]          //Movies Select
{
  PUSH:
  {
		SWITCH (nRoomSel)
		{
			CASE 1:
			{
				IF ((nSrcSel_Living != SOURCE_VOD))
				{
					if (nIPStatusLiving == ipconn_connected)
					SEND_STRING dvSTB_Living,'$HOM#'
					else
					strLivingroomCommand = '$HOM#'
				}
				WAIT 20	'Command To Living Server'
				{
					if (nIPStatusLiving == ipconn_connected)
					SEND_STRING dvSTB_Living,'$VOD#'
					else
					strLivingroomCommand = '$VOD#'
				}
				WAIT 30
				{
					cancel_wait 'Check Switcher Connectivity'
					SEND_STRING dvSW,'1*1$'
					nSrcSel_Living = SOURCE_VOD
				}
				WAIT 40 SEND_STRING dvSW,'1*1$'
				// Make sure TV is On and on right input
				if (nLCDLivPwrStat = 0)
				{
					cancel_wait 'Check LCDLiv Power'
					SEND_STRING dvLCDLiv,"'ka 1 1',13"
					PULSE[dvLCDLivIR,27]
					WAIT 7 PULSE[dvLCDLivIR,27]
					WAIT 60
					{
						cancel_wait 'Check LCDLiv Power'
						SEND_STRING dvLCDLiv,"'xb 1 60',13"
						PULSE[dvLCDLivIR,29]
					}
					nLCDLivPwrStat = 1
				}
			}
			CASE 2:
			{
				IF ((nSrcSel_Bed != SOURCE_VOD))
				{
					if (nIPStatusBed == ipconn_connected)
						SEND_STRING dvSTB_Bed,'$HOM#'
					else
						strBedroomCommand = '$HOM#'
				}
				WAIT 20	'Command To Bed Server'
				{
					if (nIPStatusBed == ipconn_connected)
						SEND_STRING dvSTB_Bed,'$VOD#'
					else
						strBedroomCommand = '$VOD#'
				}
				WAIT 30
				{
					cancel_wait 'Check Switcher Connectivity'
					SEND_STRING dvSW,'2*2$'
					nSrcSel_Bed = SOURCE_VOD
				}
				WAIT 40 SEND_STRING dvSW,'2*2$'
				// Make sure TV is On and on right input
				if (nLCDBedPwrStat = 0)
				{
					cancel_wait 'Check LCDBed Power'
					SEND_STRING dvLCDBed,"'ka 1 1',13"
					PULSE[dvLCDBedIR,27]
					WAIT 60
					{
						cancel_wait 'Check LCDbed Power'
						SEND_STRING dvLCDBed,"'kb 1 60',13"
					}
					nLCDBedPwrStat = 1
				}
				// Make sure Lift is Up
				if (nLiftStat = 0)
				{
					CANCEL_WAIT 'Lift_Down_On'
					CANCEL_WAIT 'Lift_Down_Off'
					OFF [dvLift_IO,4]
					WAIT 5 SEND_COMMAND dvLift_IO,'SET INPUT 4 HIGH'
					WAIT 10 'Lift_Up_On' ON [dvLift,8]
					WAIT 190 'Lift_Up_Off' OFF [dvLift,8]
					nLiftStat = 1
				}
			}
		}
  }
}
BUTTON_EVENT[dvTP,4]          //Music Select
{
  PUSH:
  {
		SWITCH (nRoomSel)
		{
			CASE 1:
			{
				IF ((nSrcSel_Living != SOURCE_MUSIC))
				{
					if (nIPStatusLiving == ipconn_connected)
					SEND_STRING dvSTB_Living,'$HOM#'
					else
					strLivingroomCommand = '$HOM#'
				}
				WAIT 20	'Command To Living Server'
				{
					if (nIPStatusLiving == ipconn_connected)
						SEND_STRING dvSTB_Living,'$MUS#'
					else
						strLivingroomCommand = '$MUS#'
				}
				cancel_wait 'Check Switcher Connectivity'
				SEND_STRING dvSW,'1*1$'
				WAIT 30 SEND_STRING dvSW,'1*1$'
				nSrcSel_Living = SOURCE_MUSIC
				// Make sure TV is On and on right input
				if (nLCDLivPwrStat = 0)
				{
					cancel_wait 'Check LCDLiv Power'
					SEND_STRING dvLCDLiv,"'ka 1 1',13"
					PULSE[dvLCDLivIR,27]
					WAIT 7 PULSE[dvLCDLivIR,27]
					WAIT 60
					{
						cancel_wait 'Check LCDLiv Power'
						SEND_STRING dvLCDLiv,"'xb 1 60',13"
						PULSE[dvLCDLivIR,29]
					}
					nLCDLivPwrStat = 1
				}
			}
			CASE 2:
			{
				IF ((nSrcSel_Bed != SOURCE_MUSIC))
				{
					if (nIPStatusBed == ipconn_connected)
						SEND_STRING dvSTB_Bed,'$HOM#'
					else
						strBedroomCommand = '$HOM#'
				}
				WAIT 20	'Command To Bed Server'
				{
					if (nIPStatusBed == ipconn_connected)
						SEND_STRING dvSTB_Bed,'$MUS#'
					else
						strBedroomCommand = '$MUS#'
				}
				cancel_wait 'Check Switcher Connectivity'
				SEND_STRING dvSW,'2*2$'
				WAIT 30 SEND_STRING dvSW,'2*2$'
				nSrcSel_Bed = SOURCE_MUSIC
				// Make sure TV is On and on right input
				if (nLCDBedPwrStat = 0)
				{
					cancel_wait 'Check LCDBed Power'
					SEND_STRING dvLCDBed,"'ka 1 1',13"
					PULSE[dvLCDBedIR,27]
					WAIT 60
					{
					cancel_wait 'Check LCDbed Power'
					SEND_STRING dvLCDBed,"'kb 1 60',13"
					}
					nLCDBedPwrStat = 1
				}
				// Make sure Lift is Up
				if (nLiftStat = 0)
				{
					CANCEL_WAIT 'Lift_Down_On'
					CANCEL_WAIT 'Lift_Down_Off'
					OFF [dvLift_IO,4]
					WAIT 5 SEND_COMMAND dvLift_IO,'SET INPUT 4 HIGH'
					WAIT 10 'Lift_Up_On' ON [dvLift,8]
					WAIT 190 'Lift_Up_Off' OFF [dvLift,8]
					nLiftStat = 1
				}
			}
		}
  }
}
BUTTON_EVENT[dvTP,5]          //Aux Select
{
  PUSH:
  {
		SWITCH (nRoomSel)
		{
			CASE 1:
			{
				IF ((nSrcSel_Living != SOURCE_AUX))
				{
					if (nIPStatusLiving == ipconn_connected)
						SEND_STRING dvSTB_Living,'$HOM#'
					else
						strLivingroomCommand = '$HOM#'
				}
				WAIT 20	'Command To Living Server'
				{
					if (nIPStatusLiving == ipconn_connected)
					SEND_STRING dvSTB_Living,'$AUX#'
					else
					strLivingroomCommand = '$AUX#'
				}
				cancel_wait 'Check Switcher Connectivity'
				SEND_STRING dvSW,'0*1$'
				nSrcSel_Living = SOURCE_AUX
				// Make sure TV is On and on right input
				if (nLCDLivPwrStat = 0)
				{
					cancel_wait 'Check LCDLiv Power'
					SEND_STRING dvLCDLiv,"'ka 1 1',13"
					PULSE[dvLCDLivIR,27]
					WAIT 7 PULSE[dvLCDLivIR,27]
					WAIT 60
					{
						cancel_wait 'Check LCDLiv Power'
						SEND_STRING dvLCDLiv,"'xb 1 60',13"
						PULSE[dvLCDLivIR,29]
					}
					nLCDLivPwrStat = 1
				}
			}
			CASE 2:
			{
				IF ((nSrcSel_Bed != SOURCE_AUX))
				{
					if (nIPStatusBed == ipconn_connected)
						SEND_STRING dvSTB_Bed,'$HOM#'
					else
						strBedroomCommand= '$HOM#'
				}
				WAIT 20	'Command To Bed Server'
				{
					if (nIPStatusBed == ipconn_connected)
						SEND_STRING dvSTB_Bed,'$AUX#'
					else
						strBedroomCommand = '$AUX#'
				}
				cancel_wait 'Check Switcher Connectivity'
				SEND_STRING dvSW,'0*2$'
				nSrcSel_Bed = SOURCE_AUX
				// Make sure TV is On and on right input
				if (nLCDBedPwrStat = 0)
				{
					cancel_wait 'Check LCDBed Power'
					SEND_STRING dvLCDBed,"'ka 1 1',13"
					PULSE[dvLCDBedIR,27]
					WAIT 60
					{
						cancel_wait 'Check LCDbed Power'
						SEND_STRING dvLCDBed,"'kb 1 60',13"
					}
					nLCDBedPwrStat = 1
				}
				// Make sure Lift is Up
				if (nLiftStat = 0)
				{
					CANCEL_WAIT 'Lift_Down_On'
					CANCEL_WAIT 'Lift_Down_Off'
					OFF [dvLift_IO,4]
					WAIT 5 SEND_COMMAND dvLift_IO,'SET INPUT 4 HIGH'
					WAIT 10 'Lift_Up_On' ON [dvLift,8]
					WAIT 190 'Lift_Up_Off' OFF [dvLift,8]
					nLiftStat = 1
				}
			}
		}
  }
}

BUTTON_EVENT[dvTP,7]          //Living room Vol Up
BUTTON_EVENT[dvTP,8]          //Living room Vol Down
{
  PUSH:
  {
    OFF[dvVol_Living,3]
    TO[dvVol_Living,BUTTON.INPUT.CHANNEL-6]
		nVol_Living = 0
  }
}
BUTTON_EVENT[dvTP,9]          //Living room Vol Mute
{
  PUSH:
  {
	[dvVol_Living,3] = ![dvVol_Living,3]
  }
}

BUTTON_EVENT[dvTP,11]          //DVD Play
BUTTON_EVENT[dvTP,12]          //DVD Stop
BUTTON_EVENT[dvTP,13]          //DVD Pause
BUTTON_EVENT[dvTP,14]          //DVD Next
BUTTON_EVENT[dvTP,15]          //DVD Prev
{
  PUSH:
  {
    PULSE [dvDVD,BUTTON.INPUT.CHANNEL-10]
  }
}
BUTTON_EVENT[dvTP,16]          //DVD Fwd
BUTTON_EVENT[dvTP,17]          //DVD Rev
{
  PUSH:
  {
    TO [dvDVD,BUTTON.INPUT.CHANNEL-10]
  }
}
BUTTON_EVENT[dvTP,18]          //DVD Return
{
  PUSH:
  {
    PULSE [dvDVD,54]
  }
}
BUTTON_EVENT[dvTP,19]          //DVD Subtitle
{
  PUSH:
  {
    PULSE [dvDVD,56]
  }
}
BUTTON_EVENT[dvTP,20]          //DVD Audio
{
  PUSH:
  {
    PULSE [dvDVD,89]
  }
}
BUTTON_EVENT[dvTP,21]          //DVD Repeat
{
  PUSH:
  {
    PULSE [dvDVD,90]
  }
}
BUTTON_EVENT[dvTP,22]          //DVD Menu
BUTTON_EVENT[dvTP,23]          //DVD Up
BUTTON_EVENT[dvTP,24]          //DVD Down
BUTTON_EVENT[dvTP,25]          //DVD Left
BUTTON_EVENT[dvTP,26]          //DVD Right
BUTTON_EVENT[dvTP,27]          //DVD Select
{
  PUSH:
  {
    SET_PULSE_TIME(3)
    PULSE [dvDVD,BUTTON.INPUT.CHANNEL+22]
	SET_PULSE_TIME(5)
  }
}
BUTTON_EVENT[dvTP,28]          //DVD Button 0
BUTTON_EVENT[dvTP,29]          //DVD Button 1
BUTTON_EVENT[dvTP,30]          //DVD Button 2
BUTTON_EVENT[dvTP,31]          //DVD Button 3
BUTTON_EVENT[dvTP,32]          //DVD Button 4
BUTTON_EVENT[dvTP,33]          //DVD Button 5
BUTTON_EVENT[dvTP,34]          //DVD Button 6
BUTTON_EVENT[dvTP,35]          //DVD Button 7
BUTTON_EVENT[dvTP,36]          //DVD Button 8
BUTTON_EVENT[dvTP,37]          //DVD Button 9
BUTTON_EVENT[dvTP,38]          //DVD Button +10
{
  PUSH:
  {
    SET_PULSE_TIME(3)
    PULSE [dvDVD,BUTTON.INPUT.CHANNEL-18]
	SET_PULSE_TIME(5)
  }
}
BUTTON_EVENT[dvTP,39]          //DVD Select
BUTTON_EVENT[dvTP,40]          //VCR Select
{
  PUSH:
  {
    SET_PULSE_TIME(3)
    PULSE [dvDVD,BUTTON.INPUT.CHANNEL-15]
		SET_PULSE_TIME(5)
  }
}

BUTTON_EVENT[dvTP,41]          //TV Chan Up
{
  PUSH:
  {
		SWITCH (nRoomSel)
		{
			CASE 1:
			{
				ON[dvVol_Living,3]
				if (nIPStatusLiving == ipconn_connected)
					SEND_STRING dvSTB_Living,'$PR+#'	//Use this string to control the Keyboard of the STB
				else
					strLivingroomCommand = '$PR+#'
				WAIT 20 OFF[dvVol_Living,3]
			}
			CASE 2:
			{
				ON[dvVol_Bed,3]
				if (nIPStatusBed == ipconn_connected)
					SEND_STRING dvSTB_Bed,'$PR+#'
				else
					strBedroomCommand = '$PR+#'
				WAIT 20 OFF[dvVol_Bed,3]
			}
		}
	}
}
BUTTON_EVENT[dvTP,42]          //TV Enter
{
  PUSH:
  {
		SWITCH (nRoomSel)
		{
			CASE 1:
			{
				if (nIPStatusLiving == ipconn_connected)
					SEND_STRING dvSTB_Living,'$ENT#'	//Use this string to control the Keyboard of the STB
				else
					strLivingroomCommand = '$ENT#'
			}
			CASE 2:
			{
				if (nIPStatusBed == ipconn_connected)
					SEND_STRING dvSTB_Bed,'$ENT#'
				else
					strBedroomCommand = '$ENT#'
			}
		}
	}
}
BUTTON_EVENT[dvTP,43]          //TV Chan Down
{
  PUSH:
  {
		SWITCH (nRoomSel)
		{
			CASE 1:
			{
				ON[dvVol_Living,3]
				if (nIPStatusLiving == ipconn_connected)
					SEND_STRING dvSTB_Living,'$PR-#'	//Use this string to control the Keyboard of the STB
				else
					strLivingroomCommand = '$PR-#'
				WAIT 20 OFF[dvVol_Living,3]
			}
			CASE 2:
			{
				ON[dvVol_Bed,3]
				if (nIPStatusBed == ipconn_connected)
					SEND_STRING dvSTB_Bed,'$PR-#'
				else
					strBedroomCommand = '$PR-#'
				WAIT 20 OFF[dvVol_Living,3]
			}
		}
	}
}

BUTTON_EVENT[dvTP,44]          //0
BUTTON_EVENT[dvTP,45]          //1
BUTTON_EVENT[dvTP,46]          //2
BUTTON_EVENT[dvTP,47]          //3
BUTTON_EVENT[dvTP,48]          //4
BUTTON_EVENT[dvTP,49]          //5
BUTTON_EVENT[dvTP,50]          //6
BUTTON_EVENT[dvTP,51]          //7
BUTTON_EVENT[dvTP,52]          //8
BUTTON_EVENT[dvTP,53]          //9
{
  PUSH:
  {
		integer nDigit
		nDigit = push_channel - 44
		SWITCH (nRoomSel)
		{
			CASE 1:
			{
				if (nIPStatusLiving == ipconn_connected)
					SEND_STRING dvSTB_Living,"'$',itoa(nDigit),'#'"	//Use this string to control the Keyboard of the STB
				else
					strLivingroomCommand = "'$',itoa(nDigit),'#'"
			}
			CASE 2:
			{
				if (nIPStatusBed == ipconn_connected)
					SEND_STRING dvSTB_Bed,"'$',itoa(nDigit),'#'"
				else
					strBedroomCommand = "'$',itoa(nDigit),'#'"
			}
		}
	}
}

BUTTON_EVENT[dvTP,58]          //LCDLiv_Pwr
{
  PUSH:
  {
	IF(nLCDLivPwrStat = 0)
		{
			cancel_wait 'Check LCDLiv Power'
			SEND_STRING dvLCDLiv,"'ka 1 1',13"
			PULSE[dvLCDLivIR,27]
			WAIT 7 PULSE[dvLCDLivIR,27]
			SEND_STRING dvSTB_Living,'$LON#'
			WAIT 60
			{
				cancel_wait 'Check LCDLiv Power'
				SEND_STRING dvLCDLiv,"'xb 1 60',13"
				PULSE[dvLCDLivIR,29]
			}
			nLCDLivPwrStat = 1
		}
		ELSE
		{
			cancel_wait 'Check LCDLiv Power'
			SEND_STRING dvLCDLiv,"'ka 1 0',13"
			PULSE[dvLCDLivIR,28]
			WAIT 7 PULSE[dvLCDLivIR,28]
			SEND_STRING dvSTB_Living,'$LOF#'
			SEND_STRING dvSW,'0*1$'
			nLCDLivPwrStat = 0
		}
  }
}
BUTTON_EVENT[dvTP,59]          //LCDBed_Pwr
{
  PUSH:
  {
		IF (nLCDBedPwrStat = 0)
		{
			CANCEL_WAIT 'Check LCDBed Power'
			SEND_STRING dvLCDBed,"'ka 1 1',13"
			PULSE[dvLCDBedIR,27]
			SEND_STRING dvSTB_Bed,'$LON#'
			WAIT 60
			{
				cancel_wait 'Check LCDBed Power'
				SEND_STRING dvLCDBed,"'kb 1 60',13"
				PULSE[dvLCDBedIR,91]
			}
			CANCEL_WAIT 'Lift_Down_On'
			CANCEL_WAIT 'Lift_Down_Off'
			OFF [dvLift_IO,4]
			WAIT 5 SEND_COMMAND dvLift_IO,'SET INPUT 4 HIGH'
			WAIT 10 'Lift_Up_On' ON [dvLift,8]
			WAIT 190 'Lift_Up_Off' OFF [dvLift,8]
			nLiftStat = 1
			nLCDBedPwrStat = 1
		}
		ELSE
		{
			CANCEL_WAIT 'Check LCDBed Power'
			SEND_STRING dvLCDBed,"'ka 1 0',13"
			PULSE[dvLCDBedIR,28]
			SEND_STRING dvSTB_Bed,'$LOF#'
			SEND_STRING dvSW,'0*2$'
			CANCEL_WAIT 'Lift_Up_On'
			CANCEL_WAIT 'Lift_Up_Off'
			OFF [dvLift,8]
			SEND_COMMAND dvLift_IO,'SET INPUT 4 LOW'
			WAIT 10 'Lift_Down_On' ON [dvLift_IO,4]
			WAIT 190 'Lift_Down_Off' OFF [dvLift_IO,4]
			nLiftStat = 0
			nLCDBedPwrStat = 0
		}
  }
}
BUTTON_EVENT[dvTP,66]          //Keyboard Up
{
  PUSH:
  {
		SWITCH (nRoomSel)
		{
			CASE 1:
			{
				if (nIPStatusLiving == ipconn_connected)
					SEND_STRING dvSTB_Living,'$UP#'
				else
					strLivingroomCommand = '$UP#'
			}
			CASE 2:
			{
				if (nIPStatusBed == ipconn_connected)
					SEND_STRING dvSTB_Bed,'$UP#'
				else
					strBedroomCommand = '$UP#'
			}
		}
	}
}
BUTTON_EVENT[dvTP,67]          //Keyboard Down
{
  PUSH:
  {
		SWITCH (nRoomSel)
		{
			CASE 1:
			{
				if (nIPStatusLiving == ipconn_connected)
					SEND_STRING dvSTB_Living,'$DN#'
				else
					strLivingroomCommand = '$DN#'
			}
			CASE 2:
			{
				if (nIPStatusBed == ipconn_connected)
					SEND_STRING dvSTB_Bed,'$DN#'
				else
					strBedroomCommand = '$DN#'
			}
		}
	}
}
BUTTON_EVENT[dvTP,68]          //Keyboard Left
{
  PUSH:
  {
		SWITCH (nRoomSel)
		{
			CASE 1:
			{
				if (nIPStatusLiving == ipconn_connected)
					SEND_STRING dvSTB_Living,'$LEF#'
				else
					strLivingroomCommand = '$LEF#'
			}
			CASE 2:
			{
				if (nIPStatusBed == ipconn_connected)
					SEND_STRING dvSTB_Bed,'$LEF#'
				else
					strBedroomCommand = '$LEF#'
			}
		}
	}
}
BUTTON_EVENT[dvTP,69]          //Keyboard Right
{
  PUSH:
  {
		SWITCH (nRoomSel)
		{
			CASE 1:
			{
				if (nIPStatusLiving == ipconn_connected)
					SEND_STRING dvSTB_Living,'$RIG#'
				else
					strLivingroomCommand = '$RIG#'
			}
			CASE 2:
			{
				if (nIPStatusBed == ipconn_connected)
					SEND_STRING dvSTB_Bed,'$RIG#'
				else
					strBedroomCommand = '$RIG#'
			}
		}
	}
}
BUTTON_EVENT[dvTP,70]          //Keyboard Enter
{
  PUSH:
  {
		SWITCH (nRoomSel)
		{
			CASE 1:
			{
				if (nIPStatusLiving == ipconn_connected)
					SEND_STRING dvSTB_Living,'$ENT#'
				else
					strLivingroomCommand = '$ENT#'
			}
			CASE 2:
			{
				if (nIPStatusBed == ipconn_connected)
					SEND_STRING dvSTB_Bed,'$ENT#'
				else
					strBedroomCommand = '$ENT#'
			}
		}
	}
}
BUTTON_EVENT[dvTP,71]          //Keyboard Play
{
  PUSH:
  {
		SWITCH (nRoomSel)
		{
			CASE 1:
			{
				if (nIPStatusLiving == ipconn_connected)
					SEND_STRING dvSTB_Living,'$PLY#'
				else
					strLivingroomCommand = '$PLY#'
				IF (nSrcSel_Living = SOURCE_AUX)
				{
					SEND_STRING dvSW,'4*1!'
				}
			}
			CASE 2:
			{
				if (nIPStatusBed == ipconn_connected)
					SEND_STRING dvSTB_Bed,'$PLY#'
				else
					strBedroomCommand = '$PLY#'
				IF (nSrcSel_Bed = SOURCE_AUX)
				{
					SEND_STRING dvSW,'4*2!'
				}
			}
		}
	}
}

BUTTON_EVENT[dvTP,72]          //Keyboard Stop
{
  PUSH:
  {
		SWITCH (nRoomSel)
		{
			CASE 1:
			{
				if (nIPStatusLiving == ipconn_connected)
					SEND_STRING dvSTB_Living,'$STP#'
				else
					strLivingroomCommand = '$STP#'
			}
			CASE 2:
			{
				if (nIPStatusBed == ipconn_connected)
					SEND_STRING dvSTB_Bed,'$STP#'
				else
					strBedroomCommand = '$STP#'
			}
		}
	}
}
BUTTON_EVENT[dvTP,73]          //Keyboard Pause
{
  PUSH:
  {
		SWITCH (nRoomSel)
		{
			CASE 1:
			{
				if (nIPStatusLiving == ipconn_connected)
					SEND_STRING dvSTB_Living,'$PAU#'
				else
					strLivingroomCommand = '$PAU#'
			}
			CASE 2:
			{
				if (nIPStatusBed == ipconn_connected)
					SEND_STRING dvSTB_Bed,'$PAU#'
				else
					strBedroomCommand = '$PAU#'
			}
		}
	}
}
BUTTON_EVENT[dvTP,74]          //Keyboard Next
{
  PUSH:
  {
		SWITCH (nRoomSel)
		{
			CASE 1:
			{
				if (nIPStatusLiving == ipconn_connected)
					SEND_STRING dvSTB_Living,'$NXT#'
				else
					strLivingroomCommand = '$NXT#'
			}
			CASE 2:
			{
				if (nIPStatusBed == ipconn_connected)
					SEND_STRING dvSTB_Bed,'$NXT#'
				else
					strBedroomCommand = '$NXT#'
			}
		}
	}
}
BUTTON_EVENT[dvTP,75]          //Keyboard Prev
{
  PUSH:
  {
		SWITCH (nRoomSel)
		{
			CASE 1:
			{
				if (nIPStatusLiving == ipconn_connected)
					SEND_STRING dvSTB_Living,'$PRV#'
				else
					strLivingroomCommand = '$PRV#'
			}
			CASE 2:
			{
				if (nIPStatusBed == ipconn_connected)
					SEND_STRING dvSTB_Bed,'$PRV#'
				else
					strBedroomCommand = '$PRV#'
			}
		}
	}
}
BUTTON_EVENT[dvTP,76]          //Keyboard Fwd
{
  PUSH:
  {
		SWITCH (nRoomSel)
		{
			CASE 1:
			{
				if (nIPStatusLiving == ipconn_connected)
					SEND_STRING dvSTB_Living,'$FWD#'
				else
					strLivingroomCommand = '$FWD#'
			}
			CASE 2:
			{
				if (nIPStatusBed == ipconn_connected)
					SEND_STRING dvSTB_Bed,'$FWD#'
				else
					strBedroomCommand = '$FWD#'
			}
		}
	}
}
BUTTON_EVENT[dvTP,77]          //Keyboard Rev
{
  PUSH:
  {
		SWITCH (nRoomSel)
		{
			CASE 1:
			{
				if (nIPStatusLiving == ipconn_connected)
					SEND_STRING dvSTB_Living,'$REV#'
				else
					strLivingroomCommand = '$REV#'
			}
			CASE 2:
			{
				if (nIPStatusBed == ipconn_connected)
					SEND_STRING dvSTB_Bed,'$REV#'
				else
					strBedroomCommand = '$REV#'
			}
		}
	}
}

BUTTON_EVENT[dvTP,81]          //Living Room Curtain Open
{
  PUSH:
  {
    PULSE [dvCurtainLR,3]
  }
}
BUTTON_EVENT[dvTP,82]          //Living Room Curtain Close
{
  PUSH:
  {
    PULSE [dvCurtainLR,4]
  }
}
BUTTON_EVENT[dvTP,83]          //Bed Room Curtain Open
{
  PUSH:
  {
    PULSE [dvCurtainBR,1]
  }
}
BUTTON_EVENT[dvTP,84]          //Bed Room Curtain Close
{
  PUSH:
  {
    PULSE [dvCurtainBR,2]
  }
}

BUTTON_EVENT[dvTP,85]          //Door Open
{
  PUSH:
  {
		IF (nDoor = 1)
		{
			SEND_STRING dvSW,'5*12g'

			IF (nSrcSel_Living = SOURCE_TV)
				SEND_STRING dvSW,'1*1!'
			ELSE IF (nSrcSel_Living = SOURCE_DVD)
				SEND_STRING dvSW,'3*1!'
			ELSE IF (nSrcSel_Living = SOURCE_VOD)
				SEND_STRING dvSW,'1*1!'
			ELSE IF (nSrcSel_Living = SOURCE_MUSIC)
				SEND_STRING dvSW,'1*1$'
			ELSE IF (nSrcSel_Living = SOURCE_AUX)
				SEND_STRING dvSW,'4*1!'
			ELSE
				SEND_STRING dvSW,'1*1!'

			IF (nSrcSel_Bed = SOURCE_TV)
				SEND_STRING dvSW,'2*2!'
			ELSE IF (nSrcSel_Bed = SOURCE_DVD)
				SEND_STRING dvSW,'3*2!'
			ELSE IF (nSrcSel_Bed = SOURCE_VOD)
				SEND_STRING dvSW,'2*2!'
			ELSE IF (nSrcSel_Bed = SOURCE_MUSIC)
				SEND_STRING dvSW,'2*2$'
			ELSE IF (nSrcSel_Bed = SOURCE_AUX)
				SEND_STRING dvSW,'4*2!'
			ELSE
				SEND_STRING dvSW,'2*2!'

			IF (nSrcSel_Powder = SOURCE_TV)
				SEND_STRING dvSW,'1*4$'
			ELSE IF (nSrcSel_Powder = SOURCE_DVD)
				SEND_STRING dvSW,'3*4$'
			ELSE IF (nSrcSel_Powder = SOURCE_AUX)
				SEND_STRING dvSW,'4*4$'

			IF (nSrcSel_Bath = SOURCE_TV)
				SEND_STRING dvSW,'2*3$'
			ELSE IF (nSrcSel_Bath = SOURCE_DVD)
				SEND_STRING dvSW,'3*3$'
			ELSE IF (nSrcSel_Bath = SOURCE_AUX)
				SEND_STRING dvSW,'4*3$'

			CANCEL_WAIT 'Vol_On'
			OFF[dvVol_Living,3]
			OFF[dvVol_Bed,3]
			OFF[dvVol_Bath,3]
			OFF[dvVol_Powder,3]
			PULSE [dvDoor,5]
			nDoor = 0
			if (nIPStatusLiving == ipconn_connected)
				SEND_STRING dvSTB_Living,'$DOR#'
			else
				strLivingroomCommand = '$DOR#'
			if (nIPStatusBed == ipconn_connected)
				SEND_STRING dvSTB_Bed,'$DOR#'
			else
				strBedroomCommand = '$DOR#'
			SEND_COMMAND dvTP,"'@PPK-PIP_Video'"
			IF (nSrc_Bath = SOURCE_OFF)
					SEND_STRING dvSW,'0*3$'
			IF (nSrc_Powder = SOURCE_OFF)
					SEND_STRING dvSW,'0*4$'
			IF (nLCDLivPwrStat = 2)
			{
				cancel_wait 'Check LCDLiv Power'
				SEND_STRING dvLCDLiv,"'ka 1 0',13"
				PULSE[dvLCDLivIR,28]
				WAIT 7 PULSE[dvLCDLivIR,28]
				nLCDLivPwrStat = 0
				SEND_STRING dvSW,'0*1$'
				if (nIPStatusLiving == ipconn_connected)
					SEND_STRING dvSTB_Living,'$LOF#'
				else
					strLivingroomCommand = '$LOF#'
			}
			IF (nLCDBedPwrStat = 2)
			{
				PULSE[dvLCDBedIR,28]
				CANCEL_WAIT 'Lift_Up_On'
				CANCEL_WAIT 'Lift_Up_Off'
				OFF [dvLift,8]
				SEND_COMMAND dvLift_IO,'SET INPUT 4 LOW'
				WAIT 10 'Lift_Down_On' ON [dvLift_IO,4]
				WAIT 190 'Lift_Down_Off' OFF [dvLift_IO,4]
				cancel_wait 'Check LCDBed Power'
				SEND_STRING dvLCDBed,"'ka 1 0',13"
				SEND_STRING dvSW,'0*2$'
				nLiftStat = 0
				nLCDBedPwrStat = 0
				if (nIPStatusBed == ipconn_connected)
					SEND_STRING dvSTB_Bed,'$LOF#'
				else
					strBedroomCommand = '$LOF#'
			}
		}
  }
}
BUTTON_EVENT[dvTP,86]          //Do not Disturb
{
  PUSH:
  {
		IF (nDND = 0)
		{
			ON [dvDND,6]
			if (nIPStatusLiving == ipconn_connected)
			SEND_STRING dvSTB_Living,'$DON#'
			else
			strLivingroomCommand = '$DON#'

			if (nIPStatusBed == ipconn_connected)
			SEND_STRING dvSTB_Bed,'$DON#'
			else
			strBedroomCommand = '$DON#'
			SEND_COMMAND dvTP,"'PPON-DND'"
			nDND = 1
		}
		ELSE
		{
			OFF [dvDND,6]
			if (nIPStatusLiving == ipconn_connected)
			SEND_STRING dvSTB_Living,'$DOF#'
			else
			strLivingroomCommand = '$DOF#'

			if (nIPStatusBed == ipconn_connected)
			SEND_STRING dvSTB_Bed,'$DOF#'
			else
			strBedroomCommand = '$DOF#'
			SEND_COMMAND dvTP,"'@PPK-DND'"
			nDND = 0
		}
  }
}

BUTTON_EVENT[dvTP,91]          //Weather
{
  PUSH:
  {
		SWITCH (nRoomSel)
		{
			CASE 1:
			{
				if (nIPStatusLiving == ipconn_connected)
					SEND_STRING dvSTB_Living,'$WEA#'
				else
					strLivingroomCommand = '$WEA#'
			}
			CASE 2:
			{
				if (nIPStatusBed == ipconn_connected)
					SEND_STRING dvSTB_Bed,'$WEA#'
				else
					strBedroomCommand = '$WEA#'
			}
		}
	}
}
BUTTON_EVENT[dvTP,92]          //Hotel Facilities
{
  PUSH:
	{
		SWITCH (nRoomSel)
		{
			CASE 1:
			{
				if (nIPStatusLiving == ipconn_connected)
					SEND_STRING dvSTB_Living,'$FAC#'
				else
					strLivingroomCommand = '$FAC#'
			}
			CASE 2:
			{
				if (nIPStatusBed == ipconn_connected)
					SEND_STRING dvSTB_Bed,'$FAC#'
				else
					strBedroomCommand = '$FAC#'
			}
		}
	}
}
BUTTON_EVENT[dvTP,93]          //Room Service
{
  PUSH:
  {
		SWITCH (nRoomSel)
		{
			CASE 1:
			{
				PULSE[vdvCLActions,11]
				if (nIPStatusLiving == ipconn_connected)
					SEND_STRING dvSTB_Living,'$SRV#'
				else
					strLivingroomCommand = '$SRV#'
			}
			CASE 2:
			{
				PULSE[vdvCLActions,11]
				if (nIPStatusBed == ipconn_connected)
					SEND_STRING dvSTB_Bed,'$SRV#'
				else
					strBedroomCommand = '$SRV#'
			}
		}
	}
}
BUTTON_EVENT[dvTP,94]          //Butler
{
  PUSH:
  {
		SWITCH (nRoomSel)
		{
			CASE 1:
			{
				PULSE[vdvCLActions,12]
				if (nIPStatusLiving == ipconn_connected)
					SEND_STRING dvSTB_Living,'$BUT#'
				else
					strLivingroomCommand = '$BUT#'
			}
			CASE 2:
			{
				PULSE[vdvCLActions,12]
				if (nIPStatusBed == ipconn_connected)
					SEND_STRING dvSTB_Bed,'$BUT#'
				else
					strBedroomCommand = '$BUT#'
			}
		}
	}
}
BUTTON_EVENT[dvTP,95]          //Internet
{
  PUSH:
  {
		SWITCH (nRoomSel)
		{
			CASE 1:
			{
				PULSE[vdvCLActions,17]
				if (nIPStatusLiving == ipconn_connected)
					SEND_STRING dvSTB_Living,'$INT#'
				else
					strLivingroomCommand = '$INT#'
				nSrcSel_Living = SOURCE_INTERNET
			}
			CASE 2:
			{
				if (nIPStatusBed == ipconn_connected)
					SEND_STRING dvSTB_Bed,'$INT#'
				else
					strBedroomCommand = '$INT#'
				nSrcSel_Bed	= SOURCE_INTERNET
			}
		}
	}
}
BUTTON_EVENT[dvTP,96]          //Mail
{
  PUSH:
  {
		PULSE[vdvCLActions,14]
		SWITCH (nRoomSel)
		{
			CASE 1:
			{
				if (nIPStatusLiving == ipconn_connected)
					SEND_STRING dvSTB_Living,'$MAI#'
				else
					strLivingroomCommand = '$MAI#'
			}
			CASE 2:
			{
				if (nIPStatusBed == ipconn_connected)
					SEND_STRING dvSTB_Bed,'$MAI#'
				else
					strBedroomCommand = '$MAI#'
			}
		}
	}
}
BUTTON_EVENT[dvTP,97]          //Prayer
{
  PUSH:
  {
		PULSE[vdvCLActions,15]
		SWITCH (nRoomSel)
		{
			CASE 1:
			{
				if (nIPStatusLiving == ipconn_connected)
					SEND_STRING dvSTB_Living,'$PRA#'
				else
					strLivingroomCommand = '$PRA#'
			}
			CASE 2:
			{
				if (nIPStatusBed == ipconn_connected)
					SEND_STRING dvSTB_Bed,'$PRA#'
				else
					strBedroomCommand = '$PRA#'
			}
		}
	}
}
BUTTON_EVENT[dvTP,98]          //Home
{
  PUSH:
  {
		SWITCH (nRoomSel)
		{
			CASE 1:
			{
				IF (nIPStatusLiving == ipconn_connected)
				{
					SEND_STRING dvSTB_Living,'$HOM#'
				}
				ELSE
				{
					strLivingroomCommand= '$HOM#'
				}
				CANCEL_WAIT 'Check Switcher Connectivity'
				SEND_STRING dvSW,'0*1$'
				nSrcSel_Living = SOURCE_NONE
			}
			CASE 2:
			{
				IF (nIPStatusBed == ipconn_connected)
				{
					SEND_STRING dvSTB_Bed,'$HOM#'
				}
				ELSE
				{
					strBedroomCommand = '$HOM#'
				}
				CANCEL_WAIT 'Check Switcher Connectivity'
				SEND_STRING dvSW,'0*2$'
				nSrcSel_Bed = SOURCE_NONE
			}
		}
	}
}
BUTTON_EVENT[dvTP,99]          //Back
{
  PUSH:
  {
		SWITCH (nRoomSel)
		{
			CASE 1:
			{
				if (nIPStatusLiving == ipconn_connected)
					SEND_STRING dvSTB_Living,'$BCK#'
				else
					strLivingroomCommand = '$BCK#'
				IF (nSrcSel_Living = SOURCE_AUX)
				{
					SEND_STRING dvSW,'0*1!'
				}
			}
			CASE 2:
			{
				if (nIPStatusBed == ipconn_connected)
					SEND_STRING dvSTB_Bed,'$BCK#'
				else
					strBedroomCommand = '$BCK#'
				IF (nSrcSel_Bed = SOURCE_AUX)
				{
					SEND_STRING dvSW,'0*2!'
				}
			}
		}
	}
}

BUTTON_EVENT[dvTP,111]          //Living Room Select
{
  PUSH:
  {
    nRoomSel = (BUTTON.INPUT.CHANNEL-110)
  }
}
BUTTON_EVENT[dvTP,112]          //Bed Room Select
{
  PUSH:
  {
    nRoomSel = (BUTTON.INPUT.CHANNEL-110)
  }
}

BUTTON_EVENT[dvTP,117]          //Bed room Vol Up
BUTTON_EVENT[dvTP,118]          //Bed room Vol Down
{
  PUSH:
  {
    OFF[dvVol_Bed,3]
    TO[dvVol_Bed,BUTTON.INPUT.CHANNEL-116]
		nVol_Bed = 0
  }
}
BUTTON_EVENT[dvTP,119]          //Bed room Vol Mute
{
  PUSH:
  {
	[dvVol_Bed,3] = ![dvVol_Bed,3]
  }
}

// TV Channels
BUTTON_EVENT[dvTP,121]
BUTTON_EVENT[dvTP,122]
BUTTON_EVENT[dvTP,123]
BUTTON_EVENT[dvTP,124]
BUTTON_EVENT[dvTP,125]
BUTTON_EVENT[dvTP,126]
BUTTON_EVENT[dvTP,127]
BUTTON_EVENT[dvTP,128]
BUTTON_EVENT[dvTP,129]
BUTTON_EVENT[dvTP,130]
BUTTON_EVENT[dvTP,131]
BUTTON_EVENT[dvTP,132]
BUTTON_EVENT[dvTP,133]
BUTTON_EVENT[dvTP,134]
BUTTON_EVENT[dvTP,135]
BUTTON_EVENT[dvTP,136]
BUTTON_EVENT[dvTP,137]
BUTTON_EVENT[dvTP,138]
BUTTON_EVENT[dvTP,139]
BUTTON_EVENT[dvTP,140]
BUTTON_EVENT[dvTP,141]
BUTTON_EVENT[dvTP,142]
BUTTON_EVENT[dvTP,143]
BUTTON_EVENT[dvTP,144]
BUTTON_EVENT[dvTP,145]
BUTTON_EVENT[dvTP,146]
BUTTON_EVENT[dvTP,147]
BUTTON_EVENT[dvTP,148]
BUTTON_EVENT[dvTP,149]
BUTTON_EVENT[dvTP,150]
BUTTON_EVENT[dvTP,151]
BUTTON_EVENT[dvTP,152]
BUTTON_EVENT[dvTP,153]
BUTTON_EVENT[dvTP,154]
BUTTON_EVENT[dvTP,155]
BUTTON_EVENT[dvTP,156]
BUTTON_EVENT[dvTP,157]
BUTTON_EVENT[dvTP,158]
BUTTON_EVENT[dvTP,159]
BUTTON_EVENT[dvTP,160]
BUTTON_EVENT[dvTP,161]
BUTTON_EVENT[dvTP,162]
BUTTON_EVENT[dvTP,163]
BUTTON_EVENT[dvTP,164]
BUTTON_EVENT[dvTP,165]
BUTTON_EVENT[dvTP,166]
BUTTON_EVENT[dvTP,167]
BUTTON_EVENT[dvTP,168]
BUTTON_EVENT[dvTP,169]
BUTTON_EVENT[dvTP,170]
BUTTON_EVENT[dvTP,171]
BUTTON_EVENT[dvTP,172]
BUTTON_EVENT[dvTP,173]
BUTTON_EVENT[dvTP,174]
BUTTON_EVENT[dvTP,175]
BUTTON_EVENT[dvTP,176]
BUTTON_EVENT[dvTP,177]
BUTTON_EVENT[dvTP,178]
BUTTON_EVENT[dvTP,179]
BUTTON_EVENT[dvTP,180]
BUTTON_EVENT[dvTP,181]
BUTTON_EVENT[dvTP,182]
BUTTON_EVENT[dvTP,183]
BUTTON_EVENT[dvTP,184]
BUTTON_EVENT[dvTP,185]
BUTTON_EVENT[dvTP,186]
BUTTON_EVENT[dvTP,187]
BUTTON_EVENT[dvTP,188]
BUTTON_EVENT[dvTP,189]
BUTTON_EVENT[dvTP,190]
BUTTON_EVENT[dvTP,191]
BUTTON_EVENT[dvTP,192]
BUTTON_EVENT[dvTP,193]
BUTTON_EVENT[dvTP,194]
BUTTON_EVENT[dvTP,195]
BUTTON_EVENT[dvTP,196]
BUTTON_EVENT[dvTP,197]
BUTTON_EVENT[dvTP,198]
BUTTON_EVENT[dvTP,199]
BUTTON_EVENT[dvTP,200]
BUTTON_EVENT[dvTP,201]
BUTTON_EVENT[dvTP,202]
BUTTON_EVENT[dvTP,203]
BUTTON_EVENT[dvTP,204]
BUTTON_EVENT[dvTP,205]
BUTTON_EVENT[dvTP,206]
BUTTON_EVENT[dvTP,207]
BUTTON_EVENT[dvTP,208]
BUTTON_EVENT[dvTP,209]
BUTTON_EVENT[dvTP,210]
BUTTON_EVENT[dvTP,211]
BUTTON_EVENT[dvTP,212]
BUTTON_EVENT[dvTP,213]
BUTTON_EVENT[dvTP,214]
BUTTON_EVENT[dvTP,215]
{
  PUSH:
  {
		SWITCH (nRoomSel)
		{
			CASE 1:
			{
				cancel_wait 'Check Switcher Connectivity'
				ON[dvVol_Living,3]     //11/21/2011
				SEND_STRING dvSW,'1*1$'
				nSrcSel_Living = SOURCE_TV
				if (nIPStatusLiving == ipconn_connected)
					SEND_STRING dvSTB_Living,"'$',ITOA(BUTTON.INPUT.CHANNEL-120),'#'"
				else
					strLivingroomCommand =  "'$',ITOA(BUTTON.INPUT.CHANNEL-120),'#'"
				WAIT 20 OFF[dvVol_Living,3]
			}
			CASE 2:
			{
				cancel_wait 'Check Switcher Connectivity'
				ON[dvVol_Bed,3]     //11/21/2011
				SEND_STRING dvSW,'2*2$'
				nSrcSel_Bed = SOURCE_TV
				if (nIPStatusBed == ipconn_connected)
					SEND_STRING dvSTB_Bed,"'$',ITOA(BUTTON.INPUT.CHANNEL-120),'#'"
				else
					strBedroomCommand = "'$',ITOA(BUTTON.INPUT.CHANNEL-120),'#'"
				WAIT 20 OFF[dvVol_Bed,3]
			}
		}
	}
}
BUTTON_EVENT[dvTP,222]
{
	PUSH:
	{
		IF(nRoomSel = 1)
		{
			OFF[dvVol_Living,3]
			TO[dvVol_Living,BUTTON.INPUT.CHANNEL-221]
			nVol_Living = 0
		}
		ELSE IF (nRoomSel = 2)
		{
			OFF[dvVol_Bed,3]
			TO[dvVol_Bed,BUTTON.INPUT.CHANNEL-221]
			nVol_Bed = 0
		}
	}
}
BUTTON_EVENT[dvTP,223]
{
	PUSH:
	{
		IF(nRoomSel = 1)
		{
			OFF[dvVol_Living,3]
			nVol_Living = 0
			TO[dvVol_Living,BUTTON.INPUT.CHANNEL-221]
		}
		ELSE IF (nRoomSel = 2)
		{
			OFF[dvVol_Bed,3]
			TO[dvVol_Bed,BUTTON.INPUT.CHANNEL-221]
			nVol_Bed = 0
		}
	}
}

// Temperature Display toggle between F and C
BUTTON_EVENT[dvTP,254]
{
  PUSH:
  {
	if (nTempModeDisplay == 1)
	  nTempModeDisplay = 2
	else
	  nTempModeDisplay = 1
  }
}

// TV Channels (additional)
BUTTON_EVENT[dvTP,261]  
BUTTON_EVENT[dvTP,262]
BUTTON_EVENT[dvTP,263]
BUTTON_EVENT[dvTP,264]
BUTTON_EVENT[dvTP,265]
BUTTON_EVENT[dvTP,266]
BUTTON_EVENT[dvTP,267]
BUTTON_EVENT[dvTP,268]
BUTTON_EVENT[dvTP,269]
BUTTON_EVENT[dvTP,270]
BUTTON_EVENT[dvTP,271]
BUTTON_EVENT[dvTP,272]
BUTTON_EVENT[dvTP,273]
BUTTON_EVENT[dvTP,274]
BUTTON_EVENT[dvTP,275]
BUTTON_EVENT[dvTP,276]
BUTTON_EVENT[dvTP,277]
BUTTON_EVENT[dvTP,278]
BUTTON_EVENT[dvTP,279]
BUTTON_EVENT[dvTP,280]
BUTTON_EVENT[dvTP,281]
BUTTON_EVENT[dvTP,282]
BUTTON_EVENT[dvTP,283]
BUTTON_EVENT[dvTP,284]
BUTTON_EVENT[dvTP,285]
BUTTON_EVENT[dvTP,286]
BUTTON_EVENT[dvTP,287]
BUTTON_EVENT[dvTP,288]
BUTTON_EVENT[dvTP,289]
BUTTON_EVENT[dvTP,290]
BUTTON_EVENT[dvTP,291]
BUTTON_EVENT[dvTP,292]
BUTTON_EVENT[dvTP,293]
BUTTON_EVENT[dvTP,294]
BUTTON_EVENT[dvTP,295]
BUTTON_EVENT[dvTP,296]
BUTTON_EVENT[dvTP,297]
BUTTON_EVENT[dvTP,298]
BUTTON_EVENT[dvTP,299]
BUTTON_EVENT[dvTP,300]
BUTTON_EVENT[dvTP,301]
BUTTON_EVENT[dvTP,302]
BUTTON_EVENT[dvTP,303]
BUTTON_EVENT[dvTP,304]
BUTTON_EVENT[dvTP,305]
BUTTON_EVENT[dvTP,306]
BUTTON_EVENT[dvTP,307]
BUTTON_EVENT[dvTP,308]
BUTTON_EVENT[dvTP,309]
BUTTON_EVENT[dvTP,310]
BUTTON_EVENT[dvTP,311]
BUTTON_EVENT[dvTP,312]
BUTTON_EVENT[dvTP,313]
BUTTON_EVENT[dvTP,314]
BUTTON_EVENT[dvTP,315]
BUTTON_EVENT[dvTP,316]
BUTTON_EVENT[dvTP,317]
BUTTON_EVENT[dvTP,318]
BUTTON_EVENT[dvTP,319]
BUTTON_EVENT[dvTP,320]
BUTTON_EVENT[dvTP,321]
BUTTON_EVENT[dvTP,322]
BUTTON_EVENT[dvTP,323]
BUTTON_EVENT[dvTP,324]
BUTTON_EVENT[dvTP,325]
BUTTON_EVENT[dvTP,326]
BUTTON_EVENT[dvTP,327]
BUTTON_EVENT[dvTP,328]
BUTTON_EVENT[dvTP,329]
BUTTON_EVENT[dvTP,330]
BUTTON_EVENT[dvTP,331]
BUTTON_EVENT[dvTP,332]
BUTTON_EVENT[dvTP,333]
BUTTON_EVENT[dvTP,334]
BUTTON_EVENT[dvTP,335]
BUTTON_EVENT[dvTP,336]
BUTTON_EVENT[dvTP,337]
BUTTON_EVENT[dvTP,338]
BUTTON_EVENT[dvTP,339]
BUTTON_EVENT[dvTP,340]
{
  PUSH:
  { 
		SWITCH (nRoomSel)
		{
			CASE 1:
			{	
				cancel_wait 'Check Switcher Connectivity'
				ON[dvVol_Living,3]     //11/21/2011
				SEND_STRING dvSW,'1*1$'	
				nSrcSel_Living = SOURCE_TV
				if (nIPStatusLiving == ipconn_connected)
					SEND_STRING dvSTB_Living,"'$',ITOA(BUTTON.INPUT.CHANNEL-165),'#'"
				else
					strLivingroomCommand =  "'$',ITOA(BUTTON.INPUT.CHANNEL-165),'#'"
				WAIT 20 OFF[dvVol_Living,3]
			}
			CASE 2:
			{
				cancel_wait 'Check Switcher Connectivity'
				ON[dvVol_Bed,3]     //11/21/2011
				SEND_STRING dvSW,'2*2$'	
				nSrcSel_Bed = SOURCE_TV
				if (nIPStatusBed == ipconn_connected)
					SEND_STRING dvSTB_Bed,"'$',ITOA(BUTTON.INPUT.CHANNEL-165),'#'"
				else
					strBedroomCommand = "'$',ITOA(BUTTON.INPUT.CHANNEL-165),'#'"
				WAIT 20 OFF[dvVol_Bed,3]
			}	
		}
	}
}  

(***********************************************************)
(*                FOR NI-3000 IO PORTS                     *)
(***********************************************************)

BUTTON_EVENT[dvAlarm_IO,1]          					//Fire Alarm
{
	PUSH:
  {
    //PULSE [dvDoor,5]									//Opens the door
		strFireAlarmMessage_Bed = ''
		strFireAlarmMessage_Liv = ''
		if (nIPStatusBed == IPCONN_CONNECTED)
			SEND_STRING dvSTB_Bed,'$ALA#'
		else
			strFireAlarmMessage_Bed = '$ALA#'

		if (nIPStatusLiving == IPCONN_CONNECTED)
			SEND_STRING dvSTB_Living,'$ALA#'
		else
			strFireAlarmMessage_Liv = '$ALA#'
		cancel_wait 'Check LCDLiv Power'
		SEND_STRING dvLCDLiv,"'ka 1 1',13"
		PULSE[dvLCDLivIR,27]
		WAIT 7 PULSE[dvLCDLivIR,27]
		WAIT 60
		{
			cancel_wait 'Check LCDLiv Power'
			SEND_STRING dvLCDLiv,"'xb 1 60',13"
			PULSE[dvLCDLivIR,29]
		}
		cancel_wait 'Check LCDBed Power'
		SEND_STRING dvLCDBed,"'ka 1 1',13"
		SET_PULSE_TIME(10)
		PULSE[dvLCDBedIR,27]
		SET_PULSE_TIME(5)
		WAIT 60
		{
			cancel_wait 'Check LCDBed Power'
			SEND_STRING dvLCDBed,"'kb 1 60',13"
		}
		IF (nLiftStat = 0)
		{
			CANCEL_WAIT 'Lift_Down_On'
			CANCEL_WAIT 'Lift_Down_Off'
			OFF [dvLift_IO,4]
			WAIT 5 SEND_COMMAND dvLift_IO,'SET INPUT 4 HIGH'
			WAIT 10 'Lift_Up_On' ON [dvLift,8]
			WAIT 190 'Lift_Up_Off' OFF [dvLift,8]
			nLiftStat = 1
			nLCDBedPwrStat = 1
		}
  }
	RELEASE:
  {
		strFireAlarmMessage_Bed = ''
		strFireAlarmMessage_Liv = ''
		if (nIPStatusBed == IPCONN_CONNECTED)
			SEND_STRING dvSTB_Bed,'$AOF#'
		else
			strFireAlarmMessage_Bed = '$AOF#'

		if (nIPStatusLiving == IPCONN_CONNECTED)
			SEND_STRING dvSTB_Living,'$AOF#'
		else
			strFireAlarmMessage_Liv = '$AOF#'
		WAIT 6000
		{
			SEND_STRING dvLCDLiv,"'ka 1 0',13"
			nLCDLivPwrStat = 0
			nLiftStat = 0
			nLCDBedPwrStat = 0
			SEND_STRING dvLCDBed,"'ka 1 0',13"
			CANCEL_WAIT 'Lift_Up_On'
			CANCEL_WAIT 'Lift_Up_Off'
			OFF [dvLift,8]
			SEND_COMMAND dvLift_IO,'SET INPUT 4 LOW'
			WAIT 10 'Lift_Down_On' ON [dvLift_IO,4]
			WAIT 190 'Lift_Down_Off' OFF [dvLift_IO,4]
		}
  }
}

BUTTON_EVENT[dvBell_IO,2]          						//Doorbell
{
  PUSH:
  {
    IF (nDND = 0)
    {
			PULSE [dvDoorBell,7]										//Rings the bell
			cancel_wait 'Check Switcher Connectivity'
			SEND_STRING dvSW,'Q6*3%6*4%'
			SEND_COMMAND dvTP,"'PPON-PIP_Video'"
			SEND_STRING dvSW,'5*6G'
			WAIT 5 SEND_STRING dvSW,'Q5*1$5*2$5*3$5*4$'
			SEND_COMMAND dvTP,"'WAKE'"
			IF (nLCDLivPwrStat = 0)
			{
				nLCDLivPwrStat = 2
				cancel_wait 'Check LCDLiv Power'
				SEND_STRING dvLCDLiv,"'ka 1 1',13"
				PULSE[dvLCDLivIR,27]
				WAIT 7 PULSE[dvLCDLivIR,27]
				WAIT 100 nLCDLivPwrStat = 2
				if (nIPStatusLiving == ipconn_connected)
					SEND_STRING dvSTB_Living,'$LON#'
				else
					strLivingroomCommand = '$LON#'
				WAIT 60
				{
					cancel_wait 'Check LCDLiv Power'
					SEND_STRING dvLCDLiv,"'xb 1 60',13"
					PULSE[dvLCDLivIR,29]
					if (nIPStatusLiving == ipconn_connected)
						SEND_STRING dvSTB_Living,'$CAM#'
					else
						strLivingroomCommand = '$CAM#'
				}
			}
			IF (nLCDBedPwrStat = 0)
			{
				nLCDBedPwrStat = 2
				CANCEL_WAIT 'Lift_Down_On'
				CANCEL_WAIT 'Lift_Down_Off'
				OFF [dvLift_IO,4]
				SEND_COMMAND dvLift_IO,'SET INPUT 4 HIGH'
				WAIT 10 'Lift_Up_On' ON [dvLift,8]
				WAIT 190 'Lift_Up_Off' OFF [dvLift,8]
				nLiftStat = 1
				cancel_wait 'Check LCDBed Power'
				SEND_STRING dvLCDBed,"'ka 1 1',13"
				PULSE[dvLCDBedIR,27]
				if (nIPStatusBed == ipconn_connected)
					SEND_STRING dvSTB_Bed,'$LON#'
				else
					strBedroomCommand = '$LON#'
				WAIT 60
				{
					nLCDBedPwrStat = 2
					cancel_wait 'Check LCDBed Power'
					SEND_STRING dvLCDBed,"'kb 1 60',13"
					if (nIPStatusBed == ipconn_connected)
						SEND_STRING dvSTB_Bed,'$CAM#'
					else
						strBedroomCommand = '$CAM#'
				}
			}
			if (nIPStatusLiving == ipconn_connected)
				SEND_STRING dvSTB_Living,'$CAM#'
			else
				strLivingroomCommand = '$CAM#'
			if (nIPStatusBed == ipconn_connected)
				SEND_STRING dvSTB_Bed,'$CAM#'
			else
				strBedroomCommand = '$CAM#'
			WAIT 180
			{
				SEND_STRING dvSW,'5*12g'
			}
			WAIT 260 'Vol_On'
			{
				IF (nSrcSel_Living = SOURCE_TV)
					SEND_STRING dvSW,'1*1!'
				ELSE IF (nSrcSel_Living = SOURCE_DVD)
					SEND_STRING dvSW,'3*1!'
				ELSE IF (nSrcSel_Living = SOURCE_VOD)
					SEND_STRING dvSW,'1*1!'
				ELSE IF (nSrcSel_Living = SOURCE_MUSIC)
					SEND_STRING dvSW,'1*1$'
				ELSE IF (nSrcSel_Living = SOURCE_AUX)
					SEND_STRING dvSW,'4*1!'
				ELSE
					SEND_STRING dvSW,'1*1!'

				IF (nSrcSel_Bed = SOURCE_TV)
					SEND_STRING dvSW,'2*2!'
				ELSE IF (nSrcSel_Bed = SOURCE_DVD)
					SEND_STRING dvSW,'3*2!'
				ELSE IF (nSrcSel_Bed = SOURCE_VOD)
					SEND_STRING dvSW,'2*2!'
				ELSE IF (nSrcSel_Bed = SOURCE_MUSIC)
					SEND_STRING dvSW,'2*2$'
				ELSE IF (nSrcSel_Bed = SOURCE_AUX)
					SEND_STRING dvSW,'4*2!'
				ELSE
					SEND_STRING dvSW,'2*2!'

				IF (nSrcSel_Powder = SOURCE_TV)
					SEND_STRING dvSW,'1*4$'
				ELSE IF (nSrcSel_Powder = SOURCE_DVD)
					SEND_STRING dvSW,'3*4$'
				ELSE IF (nSrcSel_Powder = SOURCE_AUX)
					SEND_STRING dvSW,'4*4$'

				IF (nSrcSel_Bath = SOURCE_TV)
					SEND_STRING dvSW,'2*3$'
				ELSE IF (nSrcSel_Bath = SOURCE_DVD)
					SEND_STRING dvSW,'3*3$'
				ELSE IF (nSrcSel_Bath = SOURCE_AUX)
					SEND_STRING dvSW,'4*3$'

				if (nIPStatusLiving == ipconn_connected)
					SEND_STRING dvSTB_Living,'$DOR#'
				else
					strLivingroomCommand = '$DOR#'

				if (nIPStatusBed == ipconn_connected)
					SEND_STRING dvSTB_Bed,'$DOR#'
				else
					strBedroomCommand = '$DOR#'
				SEND_COMMAND dvTP,"'@PPK-PIP_Video'"
				IF (nLCDBedPwrStat = 2)
				{
					WAIT 10 nLCDBedPwrStat = 0
					WAIT 10 nLiftStat = 0
					CANCEL_WAIT 'Lift_Up_On'
					CANCEL_WAIT 'Lift_Up_Off'
					OFF [dvLift,8]
					SEND_COMMAND dvLift_IO,'SET INPUT 4 LOW'
					WAIT 10 'Lift_Down_On' ON [dvLift_IO,4]
					WAIT 190 'Lift_Down_Off' OFF [dvLift_IO,4]
					cancel_wait 'Check LCDBed Power'
					SEND_STRING dvLCDBed,"'ka 1 0',13"
					PULSE[dvLCDBedIR,28]
					SEND_STRING dvSW,'0*2$'
					if (nIPStatusBed == ipconn_connected)
						SEND_STRING dvSTB_Bed,'$LOF#'
					else
						strBedroomCommand = '$LOF#'
				}
				IF (nLCDLivPwrStat = 2)
				{
					WAIT 10 nLCDLivPwrStat = 0
					cancel_wait 'Check LCDLiv Power'
					SEND_STRING dvLCDLiv,"'ka 1 0',13"
					PULSE[dvLCDLivIR,28]
					WAIT 7 PULSE[dvLCDLivIR,28]
					SEND_STRING dvSW,'0*1$'
					if (nIPStatusLiving == ipconn_connected)
						SEND_STRING dvSTB_Living,'$LOF#'
					else
						strLivingroomCommand = '$LOF#'
				}
				IF (nSrc_Bath = SOURCE_OFF)
					SEND_STRING dvSW,'0*3$'
				IF (nSrc_Powder = SOURCE_OFF)
					SEND_STRING dvSW,'0*4$'
				nDoor = 0
				OFF[dvVol_Living,3]
				OFF[dvVol_Bed,3]
				OFF[dvVol_Bath,3]
				OFF[dvVol_Powder,3]
			}
			nDoor = 1
    }
  }
}
BUTTON_EVENT[dvBell_IO,3]          						//Door PIP pop-up page off
{
  RELEASE:
  {
		IF (nDoor = 1)
		{
			if (nIPStatusLiving == ipconn_connected)
			SEND_STRING dvSTB_Living,'$DOR#'
			else
			strLivingroomCommand = '$DOR#'
			WAIT 15
			{
				if (nIPStatusLiving == ipconn_connected)
					SEND_STRING dvSTB_Living,'$DOR#'
				else
					strLivingroomCommand = '$DOR#'
			}
			if (nIPStatusBed == ipconn_connected)
				SEND_STRING dvSTB_Bed,'$DOR#'
			else
				strBedroomCommand =  '$DOR#'
			WAIT 15
			{
				if (nIPStatusBed == ipconn_connected)
					SEND_STRING dvSTB_Bed,'$DOR#'
				else
					strBedroomCommand = '$DOR#'
			}
			SEND_COMMAND dvTP,"'@PPK-PIP_Video'"
			WAIT 15 SEND_COMMAND dvTP,"'@PPK-PIP_Video'"
			nDoor = 0
		}
  }
}


(***********************************************************)
(*                FOR AXD-MSP8 KEYPADS                     *)
(***********************************************************)

BUTTON_EVENT[dvKP_Bath,1]          					//Source Select
{
  PUSH:
  {
		IF (nSrcSel_Bath = SOURCE_AUX)
		{
			cancel_wait 'Check Switcher Connectivity'
			SEND_STRING dvSW,'3*3$'
			nSrcSel_Bath = SOURCE_DVD
			IF (nSrc_Bath = SOURCE_OFF)
			{
				SEND_STRING dvSW,'3*3$'
				nSrc_Bath = SOURCE_ON
			}
		}
		ELSE IF (nSrcSel_Bath = SOURCE_DVD)
		{
			cancel_wait 'Check Switcher Connectivity'
			SEND_STRING dvSW,'2*3$'
			nSrcSel_Bath = SOURCE_TV
			IF (nSrc_Bath = SOURCE_OFF)
			{
				SEND_STRING dvSW,'2*3$'
				nSrc_Bath = SOURCE_ON
			}
		}
		ELSE IF (nSrcSel_Bath = SOURCE_TV)
		{
			cancel_wait 'Check Switcher Connectivity'
			SEND_STRING dvSW,'4*3$'
			nSrcSel_Bath = SOURCE_AUX
			IF (nSrc_Bath = SOURCE_OFF)
			{
				SEND_STRING dvSW,'4*3$'
				nSrc_Bath = SOURCE_ON
			}
		}
		nSrc_Bath = SOURCE_ON
		nSrc_Bath_Trans = 0
		nVol_Bath = 0
  }
}
BUTTON_EVENT[dvKP_Bath,2]          					//DVD Play
{
  PUSH:
  {
		PULSE [dvDVD,BUTTON.INPUT.CHANNEL-1]
		nSrc_Bath_Trans = DVD_PLAY
    nSrc_Bath = 0
		nVol_Bath = 0
  }
}
BUTTON_EVENT[dvKP_Bath,3]          					//DVD Stop
{
  PUSH:
  {
		PULSE [dvDVD,BUTTON.INPUT.CHANNEL-1]
		nSrc_Bath_Trans = DVD_STOP
    nSrc_Bath = 0
		nVol_Bath = 0
  }
}
BUTTON_EVENT[dvKP_Bath,4]          					//DVD Rew
{
  PUSH:
  {
		PULSE [dvDVD,BUTTON.INPUT.CHANNEL+1]
		nSrc_Bath_Trans = DVD_REW
    nSrc_Bath = 0
		nVol_Bath = 0
  }
  HOLD[10]:
  {
		PULSE [dvDVD,BUTTON.INPUT.CHANNEL+3]
		nSrc_Bath_Trans = DVD_REW
    nSrc_Bath = 0
		nVol_Bath = 0
	}
}
BUTTON_EVENT[dvKP_Bath,5]          					//Vol Up
{
  PUSH:
  {
    OFF[dvVol_Bath,3]
    TO[dvVol_Bath,BUTTON.INPUT.CHANNEL-4]
		nVol_Bath = 1
		nSrc_Bath_Trans = 0

  }
}
BUTTON_EVENT[dvKP_Bath,6]          					//Vol Down
{
  PUSH:
  {
    OFF[dvVol_Bath,3]
    TO[dvVol_Bath,BUTTON.INPUT.CHANNEL-4]
		nVol_Bath = 2
		nSrc_Bath_Trans = 0
  }
}
BUTTON_EVENT[dvKP_Bath,7]         					 //Off
{
  PUSH:
  {
		cancel_wait 'Check Switcher Connectivity'
    SEND_STRING dvSW,'0*3$'
		SEND_COMMAND dvVol_Bath,'P0L40%'
		nSrcSel_Bath = SOURCE_AUX
    nSrc_Bath = SOURCE_OFF
		nVol_Bath = 0
		nSrc_Bath_Trans = 0
  }
}
BUTTON_EVENT[dvKP_Bath,8]          					//DVD Fwd
{
  PUSH:
  {
		PULSE [dvDVD,BUTTON.INPUT.CHANNEL-4]
		nSrc_Bath_Trans = DVD_FWD
    nSrc_Bath = 0
		nVol_Bath = 0
	}
  HOLD[10]:
  {
		PULSE [dvDVD,BUTTON.INPUT.CHANNEL-2]
		nSrc_Bath_Trans = DVD_FWD
    nSrc_Bath = 0
		nVol_Bath = 0
	}
}

BUTTON_EVENT[dvKP_Powder,1]          				//Source Select
{
  PUSH:
  {
		IF (nSrcSel_Powder = SOURCE_AUX)
		{
			cancel_wait 'Check Switcher Connectivity'
			SEND_STRING dvSW,'3*4$'
			nSrcSel_Powder = SOURCE_DVD
			IF (nSrc_Powder = SOURCE_OFF)
			{
				SEND_STRING dvSW,'3*4$'
				nSrc_Powder = SOURCE_ON
			}
		}
		ELSE IF (nSrcSel_Powder = SOURCE_DVD)
		{
			cancel_wait 'Check Switcher Connectivity'
			SEND_STRING dvSW,'1*4$'
			nSrcSel_Powder = SOURCE_TV
			IF (nSrc_Powder = SOURCE_OFF)
			{
				SEND_STRING dvSW,'1*4$'
				nSrc_Powder = SOURCE_ON
			}
		}
		ELSE IF (nSrcSel_Powder = SOURCE_TV)
		{
			cancel_wait 'Check Switcher Connectivity'
			SEND_STRING dvSW,'4*4$'
			nSrcSel_Powder = SOURCE_AUX
			IF (nSrc_Powder = SOURCE_OFF)
			{
				SEND_STRING dvSW,'4*4$'
				nSrc_Powder = SOURCE_ON
			}
		}
		nSrc_Powder = SOURCE_ON
		nSrc_Powder_Trans = 0
		nVol_Powder = 0
  }
}
BUTTON_EVENT[dvKP_Powder,2]          				//DVD Play
{
  PUSH:
  {
		PULSE [dvDVD,BUTTON.INPUT.CHANNEL-1]
		nSrc_Powder_Trans = DVD_PLAY
		nSrc_Powder = 0
		nVol_Powder = 0
  }
}
BUTTON_EVENT[dvKP_Powder,3]          				//DVD Stop
{
  PUSH:
  {
		PULSE [dvDVD,BUTTON.INPUT.CHANNEL-1]
		nSrc_Powder_Trans = DVD_STOP
		nSrc_Powder = 0
		nVol_Powder = 0
  }
}
BUTTON_EVENT[dvKP_Powder,4]          				//DVD Rew
{
  PUSH:
  {
		PULSE [dvDVD,BUTTON.INPUT.CHANNEL+1]
		nSrc_Powder_Trans = DVD_REW
		nSrc_Powder = 0
		nVol_Powder = 0
  }
  HOLD[10]:
  {
		PULSE [dvDVD,BUTTON.INPUT.CHANNEL+3]
		nSrc_Powder_Trans = DVD_REW
    nSrc_Powder = 0
		nVol_Powder = 0
	}
}
BUTTON_EVENT[dvKP_Powder,5]          				//Vol Up
{
  PUSH:
  {
    OFF[dvVol_Powder,3]
    TO[dvVol_Powder,BUTTON.INPUT.CHANNEL-4]
		nVol_Powder = 1
		nSrc_Powder_Trans = 0
  }
}
BUTTON_EVENT[dvKP_Powder,6]          				//Vol Down
{
  PUSH:
  {
    OFF[dvVol_Powder,3]
    TO[dvVol_Powder,BUTTON.INPUT.CHANNEL-4]
		nVol_Powder = 2
		nSrc_Powder_Trans = 0
  }
}
BUTTON_EVENT[dvKP_Powder,7]          				//Off
{
  PUSH:
  {
		cancel_wait 'Check Switcher Connectivity'
    SEND_STRING dvSW,'0*4$'
		SEND_COMMAND dvVol_Powder,'P0L40%'
		nSrcSel_Powder = SOURCE_AUX
		nSrc_Powder = SOURCE_OFF
		nSrc_Powder_Trans = 0
		nVol_Powder = 0

  }
}
BUTTON_EVENT[dvKP_Powder,8]          				//DVD Fwd
{
  PUSH:
  {
		PULSE [dvDVD,BUTTON.INPUT.CHANNEL-4]
		nSrc_Powder_Trans = DVD_FWD
		nSrc_Powder = 0
		nVol_Powder = 0
	}
  HOLD[10]:
  {
		PULSE [dvDVD,BUTTON.INPUT.CHANNEL-2]
		nSrc_Powder_Trans = DVD_FWD
    nSrc_Powder = 0
		nVol_Powder = 0
	}
}


level_event[dvTP,10]	// Living Room Volume bar graph
{
  if (level.value != 0)
	off[dvVol_Living,3]
  SEND_COMMAND dvVol_Living,"'P0L',itoa(level.value),'%'"
}

level_event[dvTP,11]	// Bedroom Volume bar graph
{
  if (level.value != 0)
	off[dvVol_Bed,3]
  SEND_COMMAND dvVol_Bed,"'P0L',itoa(level.value),'%'"
}
level_event[dvVol_Living,1]
{
  nLivingroomVolumeLvl = level.value
//ferds - mar 17, 09
if (ndvtp = ipconn_connected)
  nLivingroomVolumeLvl01 = nLivingroomVolumeLvl
//ferds - mar 17, 09
}
level_event[dvVol_Bed,1]
{
  nBedroomVolumeLvl = level.value
//ferds - mar 17, 09
if (ndvtp = ipconn_connected)
  nBedroomVolumeLvl01 	 = nBedroomVolumeLvl
//ferds - mar 17, 09
}

channel_event[dvVol_Living,1]
channel_event[dvVol_Living,2]
{
  off:
  {
	send_level dvTP,10,(nLivingroomVolumeLvl * 100)/255
  }
}
channel_event[dvVol_Living,3]
{
  on:
  {
	nOldLivingroomVolumeLvl = nLivingroomVolumeLvl
	send_level dvTP,10,0
  }
  off:
  {
	send_level dvTP,10,(nOldLivingroomVolumeLvl * 100)/255
  }
}

channel_event[dvVol_Bed,1]
channel_event[dvVol_Bed,2]
{
  off:
  {
	send_level dvTP,11,(nBedroomVolumeLvl * 100)/255
  }
}
channel_event[dvVol_Bed,3]
{
  on:
  {
	nOldBedroomVolumeLvl = nBedroomVolumeLvl
	send_level dvTP,11,0
  }
  off:
  {
	send_level dvTP,11,(nOldBedroomVolumeLvl * 100)/255
  }
}

// For testing purposes
BUTTON_EVENT[virtual,0]
{
  PUSH:
  {
	switch (push_channel)
	{
	  case 1:				// Living Room Close IP connection
	  {
		if (nIPStatusLiving == ipconn_connected)
		  ip_client_close(dvSTB_Living.PORT)
	  }
	  case 2:				// Bedroom Close IP connection
	  {
		if (nIPStatusBed == ipconn_connected)
		  ip_client_close(dvSTB_Bed.PORT)
	  }
	  case 3:
	  {
		// Get current temperature
		if (nIPStatusLiving == ipconn_connected)
		  SEND_STRING dvSTB_Living,'$TEM#'
		else
		  strLivingroomCommand = '$TEM#'
	  }
	  case 4:	// get TV Channel XML File from Main Server
	  {
		IP_CLIENT_OPEN(dvServer.port,strIconServerIP,80,TCP)
	  }
	}
  }
}



(***********************************************************)
(*            THE ACTUAL PROGRAM GOES BELOW                *)
(***********************************************************)
DEFINE_PROGRAM

strBedroomMessage = remove_string(strBedroomServerBuffer,"13,10",1)
if (length_string(strBedroomMessage))
{
  nRMSBedServerDevice_Communicating = 1;
	SEND_COMMAND vdvRMSEngine,'SET PARAM-0:3:0,Device Communicating,1'
  fnParseServerMessage(strBedroomMessage, CONST_BED)
}
strLivroomMessage = remove_string(strLivroomServerBuffer,"13,10",1)
if (length_string(strLivroomMessage))
{
  nRMSLivServerDevice_Communicating = 1;
	SEND_COMMAND vdvRMSEngine,'SET PARAM-0:4:0,Device Communicating,1'
  fnParseServerMessage(strLivroomMessage, CONST_LIVING)
}
strXMLLine		= remove_string(strServerBuffer,"13,10",1)
if (length_string(strXMLLine))
{

  local_var	char		strTrash[255]

  set_length_string(strXMLLine, length_string(strXMLLine) - 2)	// remove 13,10

  // We need to look for <Channelnumber></Channelnumber> to get the order number
  // We need to look for <icon></icon> to get the icon name.

  if (find_string(strXMLLine,'</Channelnumber>',1))
  {
		strTrash 	= remove_string(strXMLLine,'<Channelnumber>',1)
		strTrash 	= remove_string(strXMLLine,'<',1)
		nIconIndex	= atoi("strTrash");

  }
  else if (find_string(strXMLLine,'</icon>',1))
  {
		strTrash 	= remove_string(strXMLLine,'<icon>',1)
		strTrash	= remove_string(strXMLLine,'</icon>',1)
		set_length_string(strTrash, length_string(strTrash) - 7)	// remove /icon>

		// what remains should be the icon file name
		if (nIconIndex)
		{
			strIconFile[nIconIndex] = strTrash

			send_string 0,"'TV ',itoa(nIconIndex),' ',strIconFile[nIconIndex]"

			// Update the TP file
			if (device_id(dvTP))
			{
				SEND_COMMAND dvTP,"'^RMF-TV_50,%Fchan50.jpg'"
				SEND_COMMAND dvTP1b,"'^BBR-50,1&2,TV_50'"

				if (nIconIndex <= MAX_ICONS)
				{
					// Modify each Dynamic Image resource Name (TV_01 .. TV_94)
					SEND_COMMAND dvTP,"'^RMF-TV_',itoa(nIconIndex/10),itoa(nIconIndex%10),',%F',strIconFile[nIconIndex]"

					// After the first Icon has been assigned to a resource, we started
					// modifying the TP images starting from the  second icon.
					// Let's now set the bitmap of a button to use a particular resource.
					// "'^BBR-<vt addr range>,<button states range>,<resource name>'"
					if (nIconIndex > 1)
					{
					if (length_string(strIconFile[nIconIndex - 1]))
						SEND_COMMAND dvTP1b,"'^BBR-',itoa(nIconIndex - 1),',1&2,TV_',itoa((nIconIndex-1)/10),itoa((nIconIndex-1)%10)"
					}
				}
			}
		}
  }
  else
  {
	strXMLLine = ''
  }
}


[dvTP,58] 		= (nLCDLivPwrStat == 1) OR (nLCDLivPwrStat == 2)
[dvTP,59] 		= (nLCDBedPwrStat == 1) OR (nLCDBedPwrStat == 2)

[dvTP,56]		= (nLiftStat = 1)
[dvTP,57]		= (nLiftStat = 0)
[dvTP,9] 		= (nVol_Living = 1)
[dvTP,111] 	= (nRoomSel = 1)
[dvTP,112] 	= (nRoomSel = 2)
[dvTP,119] 	= (nVol_Bed = 1)
[dvTP,86] 	= ([dvDND,6])

[dvKP_Powder,1] = (nSrc_Powder = SOURCE_ON)
[dvKP_Powder,2] = (nSrc_Powder_Trans = DVD_PLAY)
[dvKP_Powder,3] = (nSrc_Powder_Trans = DVD_STOP)
[dvKP_Powder,4] = (nSrc_Powder_Trans = DVD_REW)
[dvKP_Powder,5] = (nVol_Powder = 1)
[dvKP_Powder,6] = (nVol_Powder = 2)
[dvKP_Powder,7] = (nSrc_Powder = SOURCE_OFF)
[dvKP_Powder,8] = (nSrc_Powder_Trans = DVD_FWD)

[dvKP_Bath,1] 	= (nSrc_Bath = SOURCE_ON)
[dvKP_Bath,2] 	= (nSrc_Bath_Trans = DVD_PLAY)
[dvKP_Bath,3] 	= (nSrc_Bath_Trans = DVD_STOP)
[dvKP_Bath,4] 	= (nSrc_Bath_Trans = DVD_REW)
[dvKP_Bath,5] 	= (nVol_Bath = 1)
[dvKP_Bath,6] 	= (nVol_Bath = 2)
[dvKP_Bath,7] 	= (nSrc_Bath = SOURCE_OFF)
[dvKP_Bath,8] 	= (nSrc_Bath_Trans = DVD_FWD)

/////////////////////////////////////////////////////////////
//maintain IP connection
wait 10
{
  fnTCP_MaintainConnection()
}

wait 3000
{
	if (nLoadedLiving == 1)
	{
		if (length_string(strLivingroomCommand) == 0 && length_string(strFireAlarmMessage_Liv) == 0)
		SEND_STRING dvSTB_Living,'$ACO#'
	}
	if (nLoadedBedroom == 1)
	{
		if (length_string(strBedroomCommand) == 0 && length_string(strFireAlarmMessage_Bed) == 0)
		SEND_STRING dvSTB_Bed,'$ACO#'
	}
}

// Check weather condition every 30 minutes
strCurrentTime = time;
if (find_string(strCurrentTime,':30:00',1))
{
  if (nCheckWeather != 1)
  {
	nCheckWeather = 1

	// Check the weather
	if (nIPStatusBed == ipconn_connected)
	  SEND_STRING dvSTB_Bed,'$TEM#'
	else
	  strBedroomCommand = '$TEM#'
	if (nIPStatusLiving == ipconn_connected)
	  send_string dvSTB_Living,'$TEM#'
	else
	  strLivingroomCommand = '$TEM#'

	wait 100 nCheckWeather = 0
  }
}

if (find_string(strCurrentTime,'13:00:00',1) OR find_string(strCurrentTime,'02:00:00',1))
{
	IP_CLIENT_OPEN(dvServer.port,strIconServerIP,80,TCP)
}


if ([dvVol_Bed,3])
  nVol_Bed = 1
else
  nVol_Bed = 0

if ([dvVol_Living,3])
  nVol_Living = 1
else
  nVol_Living = 0

if ([dvVol_Powder,3])
  nVol_Powder = 1
else
  nVol_Powder = 0

if ([dvVol_Bath,3])
  nVol_Bath = 1
else
  nVol_Bath = 0


// Check LCD Power Status every 2 seconds
wait 20 'Check LCDLiv Power'
{
  send_string dvLCDLiv,"'ka 1 FF',13"
}
wait 20 'Check LCDBed Power'
{
  send_string dvLCDBed,"'ka 1 FF',13"
}
wait 100 'Check Switcher Connectivity'
{
  send_string dvSW,'Q'	// Version ?
}

// Temp Mode Display: OFF = F, ON=C
[dvTP,254] 	= (nTempModeDisplay == 2)


// RMS Feedback
#IF_DEFINED vdvRMSEngine
[vdvCLActions,1001] =  (nLCDLivPwrStat == 1 || nLCDBedPwrStat == 1)

[vdvCLActions,1]	=  (nSrcSel_Living == SOURCE_VOD)
[vdvCLActions,2]	=  (nSrcSel_Living == SOURCE_MUSIC)
[vdvCLActions,3]	=  (nSrcSel_Living == SOURCE_TV)
[vdvCLActions,5]	=  (nSrcSel_Living == SOURCE_DVD)
[vdvCLActions,4]	=  (nSrcSel_Living == SOURCE_AUX)

[vdvCLActions,6]	=  (nSrcSel_Bed == SOURCE_VOD)
[vdvCLActions,7]	=  (nSrcSel_Bed == SOURCE_MUSIC)
[vdvCLActions,8]	=  (nSrcSel_Bed == SOURCE_TV)
[vdvCLActions,10]	=  (nSrcSel_Bed == SOURCE_DVD)
[vdvCLActions,9]	=  (nSrcSel_Bed == SOURCE_AUX)
#END_IF

wait 50
{
  // If we have not received the Guest Name yet, keep trying
  if (length_string(strGuestName) == 0)
  {
	if (nIPStatusLiving == ipconn_connected)
	  send_string dvSTB_Living,'$TEM#'
	else
	  strLivingroomCommand = '$TEM#'
  }
}

(***********************************************************)
(*                     END OF PROGRAM                      *)
(*        DO NOT PUT ANY CODE BELOW THIS COMMENT           *)
(***********************************************************)
