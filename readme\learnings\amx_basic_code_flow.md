# AMX Programming - Basic Code Flow Analysis

## Overview
This document analyzes the essential 20% of AMX code that provides 80% understanding of the system. The example is from a hotel room automation system controlling a 1-bedroom suite.

## System Architecture

### Hardware Components
- **Living Room**: LCD TV (42"), Set-top box, Volume control
- **Bedroom**: LCD TV (32"), Set-top box, Volume control, TV lift mechanism
- **Central Control**: 8x4 Matrix switcher, Touch panel (MVP-8400)
- **Network**: TCP/IP communication with set-top boxes

### Device Definitions (Critical Section 1)
```amx
PROGRAM_NAME='BAA - 1 Bed room Suite-201 rev 12a'

DEFINE_DEVICE
// IP Servers
dvSTB_Bed       = 0:3:0         //*********
dvSTB_Living    = 0:4:0         //*********
vdvSTB_Bed      = 34001:1:0
vdvSTB_Living   = 34002:1:0

// Hardware Devices
dvSW            = 5001:1:0      //8 x 4 Matrix Switcher
dvLCDBed        = 5001:2:0      //LCD TV 32 LG
dvLCDLiv        = 5001:3:0      //LCD TV 42 LG
dvTP            = 10001:1:0     //MVP-8400 Touch Panel
```

## Source Management System

### Source Constants (Critical Section 2)
```amx
DEFINE_CONSTANT
SOURCE_TV       = 1
SOURCE_DVD      = 2
SOURCE_VOD      = 3    // Video on Demand
SOURCE_MUSIC    = 4
SOURCE_AUX      = 5
SOURCE_NONE     = 6

CONST_LIVING    = 0
CONST_BED       = 1
```

### State Variables (Critical Section 3)
```amx
DEFINE_VARIABLE
INTEGER nSrcSel_Bed     = SOURCE_NONE
INTEGER nSrcSel_Living  = SOURCE_NONE
INTEGER nLCDLivPwrStat
INTEGER nLCDBedPwrStat
INTEGER nIPStatusLiving = IPCONN_DISCONNECTED
INTEGER nIPStatusBed    = IPCONN_DISCONNECTED
INTEGER nRoomSel        // Current room selection on touch panel
```

## Communication Protocol

### TCP Connection Management (Critical Section 4)
```amx
DEFINE_FUNCTION INTEGER fnTCP_ClientConnect(INTEGER nLivingOrBed)
{
    SWITCH(nLivingOrBed)
    {
        CASE CONST_LIVING:
        {
            slResult = IP_CLIENT_OPEN(dvSTB_Living.port, cSTB_LivingAddress, lServerPort, TCP)
        }
        CASE CONST_BED:
        {
            slResult = IP_CLIENT_OPEN(dvSTB_Bed.port, cSTB_BedAddress, lServerPort, TCP)
        }
    }
}
```

### Command Protocol
- **Format**: `$COMMAND#`
- **Examples**:
  - `$TV#` - Switch to TV source
  - `$DVD#` - Switch to DVD source
  - `$VOD#` - Switch to Video on Demand
  - `$HOM#` - Return to home/off state

## Matrix Switcher Control

### Switching Commands
- **Format**: `'input*output+level'`
- **Examples**:
  - `'1*1$'` - Route input 1 to output 1 (Living room)
  - `'2*2$'` - Route input 2 to output 2 (Bedroom)
  - `'3*1!'` - Route input 3 to output 1 (DVD to Living room)

### Input/Output Mapping
- **Input 1**: TV source for Living room
- **Input 2**: TV source for Bedroom  
- **Input 3**: DVD player
- **Input 4**: AUX source
- **Output 1**: Living room display
- **Output 2**: Bedroom display

## Event-Driven Programming

### Network Events (Critical Section 5)
```amx
DATA_EVENT[dvSTB_Living]
{
    ONLINE:
    {
        nIPStatusLiving = IPCONN_CONNECTED
        // Send pending commands
    }
    OFFLINE:
    {
        nIPStatusLiving = IPCONN_DISCONNECTED
    }
    STRING:
    {
        fnParseServerMessage(data.text, CONST_LIVING)
    }
}
```

### Touch Panel Events (Critical Section 6)
```amx
BUTTON_EVENT[dvTP,1]          //TV Select Button
{
    PUSH:
    {
        SWITCH (nRoomSel)
        {
            CASE 1: // Living Room
            {
                SEND_STRING dvSTB_Living,'$TV#'
                SEND_STRING dvSW,'1*1$'
                nSrcSel_Living = SOURCE_TV
            }
            CASE 2: // Bedroom
            {
                SEND_STRING dvSTB_Bed,'$TV#'
                SEND_STRING dvSW,'2*2$'
                nSrcSel_Bed = SOURCE_TV
            }
        }
    }
}
```

## Message Processing

### Server Message Parser (Critical Section 7)
```amx
define_function fnParseServerMessage(CHAR strBuffer[], INTEGER nRoom)
{
    select
    {
        active (find_string(strMsg,'DND_ON',1)):
        {
            ON [dvDND,6]
            nDND = 1
        }
        active (find_string(strMsg,'TV',1)):
        {
            SEND_STRING dvSW,'2*2$'
            nSrcSel_Bed = SOURCE_TV
        }
        active (find_string(strMsg,'VOL_UP',1)):
        {
            PULSE[dvVol_Bed,1]
        }
    }
}
```

## Key Programming Concepts

### 1. AMX Program Structure
```
PROGRAM_NAME
DEFINE_DEVICE     → Hardware definitions
DEFINE_CONSTANT   → Fixed values
DEFINE_VARIABLE   → State variables
DEFINE_FUNCTION   → Custom functions
DEFINE_START      → Initialization code
DEFINE_EVENT      → Event handlers
```

### 2. Device Communication Types
- **Serial**: RS-232/485 for matrix switcher, TVs
- **IR**: Infrared for DVD player, TV backup control
- **IP**: TCP/IP for set-top boxes, servers

### 3. Event-Driven Architecture
- **DATA_EVENT**: Handles device communication
- **BUTTON_EVENT**: Responds to user input
- **CHANNEL_EVENT**: Monitors device states

### 4. State Management
- Variables track current system status
- Prevents conflicting commands
- Enables proper sequencing

## Control Flow Summary

1. **User Input** → Touch panel button press
2. **Room Detection** → Determine target room
3. **Command Generation** → Create appropriate commands
4. **Device Communication** → Send to set-top box and switcher
5. **State Update** → Update tracking variables
6. **Feedback** → Update touch panel display

## Best Practices Demonstrated

- **Error Handling**: Connection retry logic
- **State Tracking**: Variables prevent conflicts
- **Modular Design**: Separate functions for different tasks
- **Robust Communication**: Handle network disconnections
- **User Feedback**: Touch panel updates reflect system state

This code demonstrates a complete AMX automation system managing multiple rooms, sources, and communication protocols in a coordinated manner.
